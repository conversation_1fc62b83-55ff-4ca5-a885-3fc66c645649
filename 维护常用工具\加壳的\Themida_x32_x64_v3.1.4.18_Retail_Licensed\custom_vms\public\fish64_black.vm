/*********************************************************/
/* Machine: FISH64 (Black)
/*
/* File generated automatically from Oreans VM Generator
/* Please, do not edit
/*
/* (c) 2016 Oreans Technologies
/*********************************************************/

[Main Machine Info]

Name = (Black)
MachineId = 0x8458190A
MachineSignature = 0x300DF22A
ProductSupport = WinLicense, Virtualizer, Themida
FileVersionEncoded = 0x76281934
HardwareEncryption = Not available


[Main Machine Architecture]

Name = FISH64
Bits = 64
MaxCPUs = 8
Emulates = IA64


[Main Machine Stats]

MemoryUsage = 3500 KB
Speed = 4
Complexity = 35
ScoreMultiplier = 1


[Main Machine Processor]

RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = Yes
EnableMicroInstructions = Yes
SmartInstructionsRelocation = Yes
EnableHandlerTimes = Yes
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = Yes
PermutateHandlers = Yes
MutateHandlers = Yes


[Specific Opcodes Customization]

Group1Mnemonics = COMMON_BINARY_OP, COMMON_UNARY_OP, POP, PUSH, XCHG
Group1Garbage = [2]
Group1Avid = [5..10]
Group1Times = [3..5]

Group2Mnemonics = LODSB, LODSW, LODSD, SCASB, SCASW, SCASD, CMPSB, CMPSW, CMPSD, STOSB, STOSW, STOSD, MOVSB, MOVSW, MOVSD, PUSHFD, POPFD
Group2Garbage = [2]
Group2Avid = [2..5]
Group2Times = [2..4]

Group3Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group3Garbage = [2]
Group3Avid = [0..0]
Group3Times = [2..5]

Group4Mnemonics = ENCRYPTED_OP_0x000A
Group4Garbage = [2]
Group4Avid = [3..5]
Group4Times = [2..4]

Group5Mnemonics = ENCRYPTED_OP_0x0007, ENCRYPTED_OP_0x0009, ENCRYPTED_OP_0x0008, ENCRYPTED_OP_0x000B, ENCRYPTED_OP_0x000C, ENCRYPTED_OP_0x000D
Group5Garbage = [2]
Group5Avid = [2..5]
Group5Times = [2..4]

Group6Mnemonics = ENCRYPTED_OP_0x0006
Group6Garbage = [0]
Group6Avid = [0..0]
Group6Times = [1..3]

Group7Mnemonics = CLC, CLD, CLI, CMC, STC, STD, STI
Group7Garbage = [2]
Group7Avid = [1..2]
Group7Times = [1..2]


