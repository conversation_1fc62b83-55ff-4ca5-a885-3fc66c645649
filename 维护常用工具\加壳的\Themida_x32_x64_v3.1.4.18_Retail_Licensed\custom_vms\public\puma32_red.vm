/*********************************************************/
/* Machine: PUMA32 (Red)
/*
/* File generated automatically from Oreans VM Generator
/* Please, do not edit
/*
/* (c) 2016 Oreans Technologies
/*********************************************************/

[Main Machine Info]

Name = (Red)
MachineId = 0xEAE61305
MachineSignature = 0x69FF4DC1
ProductSupport = WinLicense, Virtualizer, Themida
FileVersionEncoded = 0x76281934
HardwareEncryption = 97Jd7N1826GTuwemaps82ysklftK726Hg4g7a81ms623H529Ks728sntJKKIIslH6Ksy1nal4l25amdtB


[Main Machine Architecture]

Name = PUMA32
Bits = 32
MaxCPUs = 1
Emulates = IA32
RevirtualizationLevel = 1


[Main Machine Stats]

MemoryUsage = 1230 KB
Speed = 17
Complexity = 87
ScoreMultiplier = 1


[Host Machine]

MachineId = 0x92858B05
NumProcessors = 1


[Guest Machine 1]

MachineId = 0x3E344705


[Host Machine Processor]

RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = Yes
EnableMicroInstructions = Yes
SmartInstructionsRelocation = Yes
EnableHandlerTimes = Yes
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = No
PermutateHandlers = No
MutateHandlers = No


[Guest1 Machine Processor]

RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = No
EnableMicroInstructions = No
SmartInstructionsRelocation = Yes
EnableHandlerTimes = No
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = No
PermutateHandlers = No
MutateHandlers = No


[Specific Host Opcodes Customization]

Group1Mnemonics = COMMON_BINARY_OP, COMMON_UNARY_OP, POP, PUSH
Group1Garbage = [6]
Group1Avid = [8..20]
Group1Times = [6..14]

Group2Mnemonics = LODSB, LODSW, LODSD, SCASB, SCASW, SCASD, CMPSB, CMPSW, CMPSD, STOSB, STOSW, STOSD, MOVSB, MOVSW, MOVSD, PUSHFD, POPFD
Group2Garbage = [5]
Group2Avid = [2..10]
Group2Times = [2..6]

Group3Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group3Garbage = [7]
Group3Avid = [0..0]
Group3Times = [7..12]


[Specific Guest1 Opcodes Customization]

Group1Mnemonics = ADD, MOV, SUB, AND, XOR, OR, POP, PUSH
Group1Garbage = [3]
Group1Avid = [1..3]
Group1Times = [1..3]

Group2Mnemonics = ROL, ROR, RCL, SHL, RCR, SHR, MOVZX, MOVSX
Group2Garbage = [2]
Group2Avid = [1..2]
Group2Times = [1..2]

Group3Mnemonics = CMP, TEST, DEC, INC, NOT, NEG
Group3Garbage = [2]
Group3Avid = [1..3]
Group3Times = [1..2]

Group4Mnemonics = IMUL, LODSB, LODSW, LODSD, SCASB, SCASW, SCASD, CMPSB, CMPSW, CMPSD, STOSB, STOSW, STOSD, MOVSB, MOVSW, MOVSD, PUSHFD, POPFD
Group4Garbage = [2]
Group4Avid = [1..3]
Group4Times = [1..2]

Group5Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group5Garbage = [3]
Group5Avid = [0..0]
Group5Times = [1..3]


