// RootDirDlg.cpp : implementation file
//

#include "stdafx.h"
#include "SFile.h"
#include "RootDirDlg.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CRootDirDlg dialog


CRootDirDlg::CRootDirDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CRootDirDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CRootDirDlg)
	m_strDir = _T("");
	//}}AFX_DATA_INIT
}


void CRootDirDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CRootDirDlg)
	DDX_Text(pDX, IDC_EDIT_ROOT_DIR, m_strDir);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CRootDirDlg, CDialog)
	//{{AFX_MSG_MAP(CRootDirDlg)
		// NOTE: the ClassWizard will add message map macros here
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CRootDirDlg message handlers

BOOL CRootDirDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();

	// TODO: Add extra initialization here

	//CMainFrame* Frm = (CMainFrame*)::AfxGetMainWnd();	
	//SfileExtractDlg *dlg = Frm->m_extractDlg;

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}
