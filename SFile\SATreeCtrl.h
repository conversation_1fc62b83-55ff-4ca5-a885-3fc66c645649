#if !defined(AFX_SATREECTRL_H__AA00ECE8_4C3E_4179_87C7_BC841A3A8C8D__INCLUDED_)
#define AFX_SATREECTRL_H__AA00ECE8_4C3E_4179_87C7_BC841A3A8C8D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SATreeCtrl.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// SATreeCtrl window

class SATreeCtrl : public CTreeCtrl
{
// Construction
public:
	SATreeCtrl();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SATreeCtrl)
	//}}AFX_VIRTUAL

// Implementation
public:
	void InsertItem( HTREEITEM parent, SADir *dir, char *path );
	virtual ~SATreeCtrl();

	// Generated message map functions
protected:
	//{{AFX_MSG(SATreeCtrl)
	afx_msg void OnSelchanged(NMHDR* pNMHDR, LRESULT* pResult);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SATREECTRL_H__AA00ECE8_4C3E_4179_87C7_BC841A3A8C8D__INCLUDED_)
