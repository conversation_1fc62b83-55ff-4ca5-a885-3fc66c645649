#if !defined(AFX_DELETELIST_H__5E0D2494_6F31_4166_8755_25F3C7338F4D__INCLUDED_)
#define AFX_DELETELIST_H__5E0D2494_6F31_4166_8755_25F3C7338F4D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DeleteList.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDeleteList window
#include <list>

class CDeleteList : public CListBox
{
// Construction
public:
	CDeleteList();

// Attributes
public:
	bool AddDropFile( char *szFileName );

// Operations
public:
// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDeleteList)
	//}}AFX_VIRTUAL

// Implementation
public:
	bool SaveList( char *filename );
	void DeleteList( int index );
	void ReleaseList();
	void SearchDir( CString &dir );
	CString m_strRootDir;
	virtual ~CDeleteList();

	std::list<char*> m_deleteL;
	// Generated message map functions
protected:
	//{{AFX_MSG(CDeleteList)
		afx_msg void OnDropFiles(HDROP hDropInfo);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DELETELIST_H__5E0D2494_6F31_4166_8755_25F3C7338F4D__INCLUDED_)
