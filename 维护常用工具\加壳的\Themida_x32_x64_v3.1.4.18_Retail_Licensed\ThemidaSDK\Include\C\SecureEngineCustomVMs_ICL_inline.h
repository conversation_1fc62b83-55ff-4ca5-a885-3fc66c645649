/******************************************************************************
 * Header: SecureEngineCustomVMs_ICL_inline.h
 * Description: ICL inline assembly macros definitions
 *
 * Author/s: Oreans Technologies 
 * (c) 2021 Oreans Technologies
 *
 * --- File generated automatically from Oreans VM Generator (14/5/2021) ---
 ******************************************************************************/

/***********************************************
 * Definition as inline assembly
 ***********************************************/

#ifdef PLATFORM_X32

#ifndef VM_TIGER_WHITE_START
#define VM_TIGER_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x64 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_WHITE_END
#define VM_TIGER_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF4 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_RED_START
#define VM_TIGER_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x65 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_RED_END
#define VM_TIGER_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF5 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_BLACK_START
#define VM_TIGER_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x66 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_BLACK_END
#define VM_TIGER_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF6 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_WHITE_START
#define VM_FISH_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6A \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_WHITE_END
#define VM_FISH_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFA \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_RED_START
#define VM_FISH_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6C \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_RED_END
#define VM_FISH_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFC \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_BLACK_START
#define VM_FISH_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6E \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_BLACK_END
#define VM_FISH_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFE \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_WHITE_START
#define VM_PUMA_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x70 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_WHITE_END
#define VM_PUMA_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x00 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_RED_START
#define VM_PUMA_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x72 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_RED_END
#define VM_PUMA_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x02 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_BLACK_START
#define VM_PUMA_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x74 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_BLACK_END
#define VM_PUMA_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x04 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_WHITE_START
#define VM_SHARK_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x76 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_WHITE_END
#define VM_SHARK_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x06 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_RED_START
#define VM_SHARK_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x78 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_RED_END
#define VM_SHARK_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x08 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_BLACK_START
#define VM_SHARK_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x7A \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_BLACK_END
#define VM_SHARK_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x0A \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_WHITE_START
#define VM_DOLPHIN_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x86 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_WHITE_END
#define VM_DOLPHIN_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x16 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_RED_START
#define VM_DOLPHIN_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x88 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_RED_END
#define VM_DOLPHIN_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x18 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_BLACK_START
#define VM_DOLPHIN_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x8A \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_BLACK_END
#define VM_DOLPHIN_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x1A \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_WHITE_START
#define VM_EAGLE_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x92 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_WHITE_END
#define VM_EAGLE_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x22 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_RED_START
#define VM_EAGLE_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x94 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_RED_END
#define VM_EAGLE_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x24 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_BLACK_START
#define VM_EAGLE_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x96 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_BLACK_END
#define VM_EAGLE_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x26 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_WHITE_START
#define VM_LION_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA0 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_WHITE_END
#define VM_LION_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x30 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_RED_START
#define VM_LION_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA2 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_RED_END
#define VM_LION_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x32 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_BLACK_START
#define VM_LION_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA4 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_BLACK_END
#define VM_LION_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x34 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_MUTATE_ONLY_START
#define VM_MUTATE_ONLY_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x10 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_MUTATE_ONLY_END
#define VM_MUTATE_ONLY_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x11 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#endif

#ifdef PLATFORM_X64

#ifndef VM_TIGER_WHITE_START
#define VM_TIGER_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x67 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_WHITE_END
#define VM_TIGER_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF7 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_RED_START
#define VM_TIGER_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x68 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_RED_END
#define VM_TIGER_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF8 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_BLACK_START
#define VM_TIGER_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x69 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_TIGER_BLACK_END
#define VM_TIGER_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xF9 \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_WHITE_START
#define VM_FISH_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6B \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_WHITE_END
#define VM_FISH_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFB \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_RED_START
#define VM_FISH_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6D \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_RED_END
#define VM_FISH_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFD \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_BLACK_START
#define VM_FISH_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x6F \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_FISH_BLACK_END
#define VM_FISH_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xFF \
  __asm __emit 0x01 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_WHITE_START
#define VM_PUMA_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x71 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_WHITE_END
#define VM_PUMA_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x01 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_RED_START
#define VM_PUMA_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x73 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_RED_END
#define VM_PUMA_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x03 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_BLACK_START
#define VM_PUMA_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x75 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_PUMA_BLACK_END
#define VM_PUMA_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x05 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_WHITE_START
#define VM_SHARK_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x77 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_WHITE_END
#define VM_SHARK_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x07 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_RED_START
#define VM_SHARK_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x79 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_RED_END
#define VM_SHARK_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x09 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_BLACK_START
#define VM_SHARK_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x7B \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_SHARK_BLACK_END
#define VM_SHARK_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x0B \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_WHITE_START
#define VM_DOLPHIN_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x87 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_WHITE_END
#define VM_DOLPHIN_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x17 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_RED_START
#define VM_DOLPHIN_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x89 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_RED_END
#define VM_DOLPHIN_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x19 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_BLACK_START
#define VM_DOLPHIN_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x8B \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_DOLPHIN_BLACK_END
#define VM_DOLPHIN_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x1B \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_WHITE_START
#define VM_EAGLE_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x93 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_WHITE_END
#define VM_EAGLE_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x23 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_RED_START
#define VM_EAGLE_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x95 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_RED_END
#define VM_EAGLE_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x25 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_BLACK_START
#define VM_EAGLE_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x97 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_EAGLE_BLACK_END
#define VM_EAGLE_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x27 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_WHITE_START
#define VM_LION_WHITE_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA1 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_WHITE_END
#define VM_LION_WHITE_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x31 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_RED_START
#define VM_LION_RED_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA3 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_RED_END
#define VM_LION_RED_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x33 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_BLACK_START
#define VM_LION_BLACK_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0xA5 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_LION_BLACK_END
#define VM_LION_BLACK_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x35 \
  __asm __emit 0x02 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_MUTATE_ONLY_START
#define VM_MUTATE_ONLY_START \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x10 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#ifndef VM_MUTATE_ONLY_END
#define VM_MUTATE_ONLY_END \
  __asm __emit 0xEB \
  __asm __emit 0x10 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 \
  __asm __emit 0x11 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x00 \
  __asm __emit 0x57\
  __asm __emit 0x4C\
  __asm __emit 0x20 \
  __asm __emit 0x20 
#endif

#endif

