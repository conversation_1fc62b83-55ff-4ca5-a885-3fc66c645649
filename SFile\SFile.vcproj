<?xml version="1.0" encoding="ks_c_5601-1987"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="SFile"
	ProjectGUID="{376E6B5F-D374-42C0-B77A-18C8A6BBA3EF}"
	Keyword="MFCProj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory=".\Release"
			IntermediateDirectory=".\Release"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="1"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/SFile.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Release/SFile.pch"
				AssemblerListingLocation=".\Release/"
				ObjectFile=".\Release/"
				ProgramDataBaseFileName=".\Release/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1042"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="winmm.lib"
				OutputFile=".\Release/SFile.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				ProgramDatabaseFile=".\Release/SFile.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory=".\Debug"
			IntermediateDirectory=".\Debug"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/SFile.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Debug/SFile.pch"
				AssemblerListingLocation=".\Debug/"
				ObjectFile=".\Debug/"
				ProgramDataBaseFileName=".\Debug/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1042"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="winmm.lib"
				OutputFile=".\Debug/SFile.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\Debug/SFile.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="MainFrm.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SFile.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SFile.rc"
				>
			</File>
			<File
				RelativePath="SFileDoc.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SFileView.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StdAfx.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						UsePrecompiledHeader="1"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<Filter
				Name="Create"
				>
				<File
					RelativePath="SfileDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileDlg.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Update"
				>
				<File
					RelativePath="SfileUpdateDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileUpdateDlg.h"
					>
				</File>
				<File
					RelativePath="UpdateList.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="UpdateList.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Extract"
				>
				<File
					RelativePath="SATreeCtrl.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SATreeCtrl.h"
					>
				</File>
				<File
					RelativePath="SfileEmptyListDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileEmptyListDlg.h"
					>
				</File>
				<File
					RelativePath="SfileExtractDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileExtractDlg.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Delete"
				>
				<File
					RelativePath="DeleteList.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="DeleteList.h"
					>
				</File>
				<File
					RelativePath="RootDirDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileDeleteListDlg.cpp"
					>
					<FileConfiguration
						Name="Release|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="2"
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							Optimization="0"
							PreprocessorDefinitions=""
							BasicRuntimeChecks="3"
							BrowseInformation="1"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="SfileDeleteListDlg.h"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="MainFrm.h"
				>
			</File>
			<File
				RelativePath="Resource.h"
				>
			</File>
			<File
				RelativePath="RootDirDlg.h"
				>
			</File>
			<File
				RelativePath="SFile.h"
				>
			</File>
			<File
				RelativePath="SFileDoc.h"
				>
			</File>
			<File
				RelativePath="SFileView.h"
				>
			</File>
			<File
				RelativePath="StdAfx.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
			>
			<File
				RelativePath=".\res\rt_manif.bin"
				>
			</File>
			<File
				RelativePath="res\SFile.ico"
				>
			</File>
			<File
				RelativePath="res\SFile.rc2"
				>
			</File>
			<File
				RelativePath="res\SFileDoc.ico"
				>
			</File>
			<File
				RelativePath="res\Toolbar.bmp"
				>
			</File>
		</Filter>
		<Filter
			Name="SAFILE"
			>
			<File
				RelativePath="SAFile.cpp"
				>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						BrowseInformation="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SAFile.h"
				>
			</File>
		</Filter>
		<File
			RelativePath="ReadMe.txt"
			>
		</File>
	</Files>
	<Globals>
		<Global
			Name="RESOURCE_FILE"
			Value="SFile.rc"
		/>
	</Globals>
</VisualStudioProject>
