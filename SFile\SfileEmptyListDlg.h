#if !defined(AFX_SFILEEMPTYLISTDLG_H__E9118168_9DE7_4336_A927_2CAF78EB8337__INCLUDED_)
#define AFX_SFILEEMPTYLISTDLG_H__E9118168_9DE7_4336_A927_2CAF78EB8337__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SfileEmptyListDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// SfileEmptyListDlg dialog

class SfileEmptyListDlg : public CDialog
{
// Construction
public:
	SfileEmptyListDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(SfileEmptyListDlg)
	enum { IDD = IDD_DLG_SFILE_EMPTYLIST };
	CListCtrl	m_listCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SfileEmptyListDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(SfileEmptyListDlg)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEEMPTYLISTDLG_H__E9118168_9DE7_4336_A927_2CAF78EB8337__INCLUDED_)
