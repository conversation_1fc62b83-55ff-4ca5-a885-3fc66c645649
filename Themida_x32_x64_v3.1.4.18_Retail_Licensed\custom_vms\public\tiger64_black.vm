/*********************************************************/
/* Machine: TIGER64 (Black)
/*
/* File generated automatically from Oreans VM Generator
/* Please, do not edit
/*
/* (c) 2016 Oreans Technologies
/*********************************************************/

[Main Machine Info]

Name = (Black)
MachineId = 0x28E9D50A
MachineSignature = 0x5A40D490
ProductSupport = WinLicense, Virtualizer, Themida
FileVersionEncoded = 0x76281934
HardwareEncryption = Not available


[Main Machine Architecture]

Name = TIGER64
Bits = 64
MaxCPUs = 8
Emulates = IA64


[Main Machine Stats]

MemoryUsage = 3000 KB
Speed = 91
Complexity = 23
ScoreMultiplier = 1


[Main Machine Processor]

RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = Yes
EnableMicroInstructions = Yes
SmartInstructionsRelocation = Yes
EnableHandlerTimes = Yes
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = No
PermutateHandlers = No
MutateHandlers = No


[Specific Opcodes Customization]

Group1Mnemonics = ADD, MOV, SUB, AND, XOR, OR, POP, PUSH
Group1Garbage = [5]
Group1Avid = [5..15]
Group1Times = [2..6]

Group2Mnemonics = ROL, ROR, RCL, SHL, RCR, SHR, MOVZX, MOVSX, XCHG
Group2Garbage = [4]
Group2Avid = [4..8]
Group2Times = [1..4]

Group3Mnemonics = CMP, TEST, DEC, INC, NOT, NEG
Group3Garbage = [5]
Group3Avid = [4..10]
Group3Times = [1..4]

Group4Mnemonics = IMUL, LODSB, LODSW, LODSD, LODSQ, SCASB, SCASW, SCASD, SCASQ, CMPSB, CMPSW, CMPSD, CMPSQ, STOSB, STOSW, STOSD, STOSQ, MOVSB, MOVSW, MOVSD, MOVSQ, PUSHFD, POPFD
Group4Garbage = [4]
Group4Avid = [4..10]
Group4Times = [1..3]

Group5Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group5Garbage = [5]
Group5Avid = [0..0]
Group5Times = [3..7]

Group6Mnemonics = ENCRYPTED_OP_0x000A
Group6Garbage = [5]
Group6Avid = [5..10]
Group6Times = [4..6]

Group7Mnemonics = ENCRYPTED_OP_0x0007, ENCRYPTED_OP_0x0009, ENCRYPTED_OP_0x0008, ENCRYPTED_OP_0x000B, ENCRYPTED_OP_0x000C, ENCRYPTED_OP_0x000D
Group7Garbage = [5]
Group7Avid = [3..8]
Group7Times = [2..5]

Group8Mnemonics = ENCRYPTED_OP_0x0006
Group8Garbage = [0]
Group8Avid = [0..0]
Group8Times = [3..6]

Group9Mnemonics = ENCRYPTED_OP_0x000E
Group9Garbage = [5]
Group9Avid = [0..0]
Group9Times = [3..6]

Group10Mnemonics = CLC, CLD, CLI, CMC, STC, STD, STI
Group10Garbage = [3]
Group10Avid = [2..4]
Group10Times = [2..4]

Group11Mnemonics = ENCRYPTED_OP_0x000F
Group11Garbage = [3]
Group11Avid = [2..4]
Group11Times = [1..1]


