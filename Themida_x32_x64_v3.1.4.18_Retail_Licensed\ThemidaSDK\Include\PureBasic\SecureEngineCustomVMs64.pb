; ******************************************************************************
; Header: SecureEngineCustomVMs64.pb
; Description: PureBasic64 macros definitions
;
; Author/s: Oreans Technologies 
; (c) 2021 Oreans Technologies
;
; --- File generated automatically from Oreans VM Generator (14/5/2021) ---
; ******************************************************************************

; ******************************************************************************
;                                 Macros definition
; ******************************************************************************

Procedure VM_TIGER_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 103
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_TIGER_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 503
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_TIGER_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 104
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_TIGER_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 504
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_TIGER_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 105
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_TIGER_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 505
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 107
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 507
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 109
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 509
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 111
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_FISH_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 511
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 113
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 513
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 115
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 515
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 117
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_PUMA_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 517
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 119
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 519
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 121
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 521
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 123
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_SHARK_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 523
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 135
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 535
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 137
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 537
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 139
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_DOLPHIN_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 539
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 147
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 547
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 149
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 549
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 151
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_EAGLE_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 551
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_WHITE_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 161
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_WHITE_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 561
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_RED_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 163
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_RED_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 563
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_BLACK_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 165
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_LION_BLACK_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 565
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_MUTATE_ONLY_START()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 16
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VM_MUTATE_ONLY_END()

    !DB 0x50, 0x53, 0x51
    !DB 0xB8, 0x57, 0x4C, 0x00, 0x00, 0xBB
    !DD 17
    !DB 0xB9, 0x57, 0x4C, 0x00, 0x00
    !DB 0x03, 0xD8
    !DB 0x03, 0xC8
    !DB 0x59, 0x5B, 0x58

EndProcedure


