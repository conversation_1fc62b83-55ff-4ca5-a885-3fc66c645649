#if !defined(AFX_SFILEUPDATEDLG_H__D622CA6D_19F9_4E1E_A519_EE1CD83C158C__INCLUDED_)
#define AFX_SFILEUPDATEDLG_H__D622CA6D_19F9_4E1E_A519_EE1CD83C158C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SfileUpdateDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// SfileUpdateDlg dialog

#include "UpdateList.h"

class SfileUpdateDlg : public CDialog
{
public:
	SADir*			m_saDirS;
	CString			m_strOpenFileName;
	unsigned int	m_nSrcFileCnt;
	unsigned int	m_nUpdateFileCnt;
	unsigned int	m_nSrcVer, m_nUpdateVer;
	unsigned int	m_nProgressCnt;
	std::list<SAFile*> m_emptyL;
	DWORD			m_dwGarbageSize;
	unsigned int	m_reserve[10];

	int	m_fhS;	//source
	int m_fhSH;
	int m_fhU;	//update
	int m_fhUH;
	int m_fhTmp;
	
	CString m_strSrcF, m_strSrcH, m_strUpdateF, m_strUpdateH, m_strUpdateL, m_strTmp;

public:
	void SetDlgItemEnable( BOOL flg );
	//bool LoadSAFile( char *filename );
	void ReadSAFile( int &fh, SADir *saDir );
	void ReadEmptyList( int &fh );
	void ReadGarbageSize( int &fh );
	void MakeUpdateFileList();
	void DeleteSrcFileList( char *filename );
	bool CopyUpdateFile();
	bool ChkEmptyList( unsigned int size, __int64 &offset );
	bool WriteFile( UpdateFile *uf, __int64 offset );
	bool AppendFile( UpdateFile *uf );
	void UpdateSrcFileList( char *filename, SAFile *file );
	void SaveHeader( SADir *dir );
	void SaveHeaderEmptyList( int &fh );
	void SaveHeaderGarbageSize( int &fh );
	void DeleteEmptyList();
	void DeleteFileList(SADir *dir);
	void Release();
	
// Construction
	SfileUpdateDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(SfileUpdateDlg)
	enum { IDD = IDD_DLG_SFILE_UPDATE };
	CProgressCtrl	m_prgress;
	CUpdateList	m_list;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SfileUpdateDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(SfileUpdateDlg)
	afx_msg void OnButtonSafile();
	afx_msg void OnButtonRoot();
	afx_msg void OnDestroy();
	virtual BOOL OnInitDialog();
	afx_msg void OnButtonStart();
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEUPDATEDLG_H__D622CA6D_19F9_4E1E_A519_EE1CD83C158C__INCLUDED_)
