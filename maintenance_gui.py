#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维流程GUI工具
用于处理运营和技术文件夹的签名前处理
"""

import os
import sys
import json
import zipfile
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
import logging
from datetime import datetime

# 尝试导入rarfile，如果失败则使用命令行工具
try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False

class MaintenanceGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("运维流程处理工具")
        self.root.geometry("800x600")
        
        # 配置文件路径
        self.config_file = "maintenance_config.json"
        self.config = self.load_config()
        
        # 选中的文件夹
        self.selected_folders = []
        self.operation_folder = None  # 运营文件夹
        self.technical_folder = None  # 技术文件夹
        self.selected_items = set()  # 记录已选中的树节点
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 加载默认路径
        if self.config.get("data_folder"):
            self.data_folder_var.set(self.config["data_folder"])
            self.refresh_folder_view()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}")
        return {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('maintenance')
        self.logger.setLevel(logging.INFO)
        
        # 创建日志处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)  # 文件夹树列可以扩展
        main_frame.rowconfigure(3, weight=1)     # 文件夹选择行可以扩展

        # 数据文件夹选择
        ttk.Label(main_frame, text="数据文件夹:").grid(row=0, column=0, sticky=tk.W, pady=5)

        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        folder_frame.columnconfigure(0, weight=1)

        self.data_folder_var = tk.StringVar(value=self.config.get("data_folder", ""))
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.data_folder_var)
        self.folder_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).grid(row=0, column=1)

        # Themida路径配置
        ttk.Label(main_frame, text="Themida路径:").grid(row=1, column=0, sticky=tk.W, pady=5)

        themida_frame = ttk.Frame(main_frame)
        themida_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        themida_frame.columnconfigure(0, weight=1)

        self.themida_path_var = tk.StringVar(value=self.config.get("themida_path", r"f:\维护\维护常用工具\加壳的\Themida_x32_x64_v3.1.4.18_Retail_Licensed\Themida64.exe"))
        self.themida_entry = ttk.Entry(themida_frame, textvariable=self.themida_path_var)
        self.themida_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(themida_frame, text="浏览", command=self.browse_themida).grid(row=0, column=1)

        # 受保护文件输出目录配置
        ttk.Label(main_frame, text="protected文件输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)

        protected_frame = ttk.Frame(main_frame)
        protected_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        protected_frame.columnconfigure(0, weight=1)

        self.protected_output_var = tk.StringVar(value=self.config.get("protected_output_dir", ""))
        self.protected_entry = ttk.Entry(protected_frame, textvariable=self.protected_output_var)
        self.protected_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(protected_frame, text="浏览", command=self.browse_protected_output).grid(row=0, column=1)

        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        # 签名后处理配置区域
        post_sign_label = ttk.Label(main_frame, text="签名后处理配置", font=('TkDefaultFont', 9, 'bold'))
        post_sign_label.grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # launcher.shaiya文件路径配置
        ttk.Label(main_frame, text="launcher.shaiya路径:").grid(row=5, column=0, sticky=tk.W, pady=5)

        launcher_frame = ttk.Frame(main_frame)
        launcher_frame.grid(row=5, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        launcher_frame.columnconfigure(0, weight=1)

        self.launcher_path_var = tk.StringVar(value=self.config.get("launcher_path", ""))
        self.launcher_entry = ttk.Entry(launcher_frame, textvariable=self.launcher_path_var)
        self.launcher_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(launcher_frame, text="浏览", command=self.browse_launcher).grid(row=0, column=1)

        # .bin文件输出目录配置
        ttk.Label(main_frame, text=".bin文件输出目录:").grid(row=6, column=0, sticky=tk.W, pady=5)

        bin_output_frame = ttk.Frame(main_frame)
        bin_output_frame.grid(row=6, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        bin_output_frame.columnconfigure(0, weight=1)

        self.bin_output_var = tk.StringVar(value=self.config.get("bin_output_dir", ""))
        self.bin_output_entry = ttk.Entry(bin_output_frame, textvariable=self.bin_output_var)
        self.bin_output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(bin_output_frame, text="浏览", command=self.browse_bin_output).grid(row=0, column=1)

        # 文件夹选择区域
        folder_selection_frame = ttk.Frame(main_frame)
        folder_selection_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        folder_selection_frame.columnconfigure(1, weight=1)
        folder_selection_frame.rowconfigure(0, weight=1)

        # 左侧按钮框架
        left_button_frame = ttk.Frame(folder_selection_frame)
        left_button_frame.grid(row=0, column=0, sticky=(tk.N, tk.W), padx=(0, 10))

        ttk.Label(left_button_frame, text="文件夹选择:").pack(anchor=tk.W)
        ttk.Button(left_button_frame, text="选择文件夹", command=self.select_current_folder).pack(pady=2, fill=tk.X)
        ttk.Button(left_button_frame, text="清除选择", command=self.clear_selection).pack(pady=2, fill=tk.X)

        # 文件夹树形视图
        tree_frame = ttk.Frame(folder_selection_frame)
        tree_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # 创建文件夹列表框（类似Windows资源管理器）
        self.folder_listbox = tk.Listbox(tree_frame, selectmode=tk.SINGLE)
        self.folder_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        listbox_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.folder_listbox.yview)
        listbox_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.folder_listbox.configure(yscrollcommand=listbox_scroll.set)

        # 绑定事件
        self.folder_listbox.bind('<Double-Button-1>', self.on_folder_double_click)
        self.folder_listbox.bind('<Button-1>', self.on_folder_single_click)

        # 当前路径显示和导航
        self.current_path = ""
        self.path_history = []  # 路径历史

        # 导航框架
        nav_frame = ttk.Frame(tree_frame)
        nav_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        nav_frame.columnconfigure(1, weight=1)

        ttk.Button(nav_frame, text="上级", command=self.go_up).grid(row=0, column=0, padx=(0, 5))
        self.path_label = ttk.Label(nav_frame, text="", relief="sunken", anchor="w")
        self.path_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        ttk.Button(button_frame, text="刷新", command=self.refresh_folder_view).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="签名前处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)

        # 签名后处理区域
        post_sign_frame = ttk.LabelFrame(main_frame, text="签名后处理", padding="5")
        post_sign_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        post_sign_frame.columnconfigure(1, weight=1)

        # 签名文件选择
        ttk.Label(post_sign_frame, text="签名后的game.exe:").grid(row=0, column=0, sticky=tk.W, pady=5)

        signed_game_frame = ttk.Frame(post_sign_frame)
        signed_game_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        signed_game_frame.columnconfigure(0, weight=1)

        self.signed_game_var = tk.StringVar()
        self.signed_game_entry = ttk.Entry(signed_game_frame, textvariable=self.signed_game_var)
        self.signed_game_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(signed_game_frame, text="浏览", command=self.browse_signed_game).grid(row=0, column=1)

        # 签名后处理按钮
        ttk.Button(post_sign_frame, text="开始签名后处理", command=self.start_post_sign_processing).grid(row=1, column=0, columnspan=3, pady=10)

        # 右侧清除日志按钮
        log_button_frame = ttk.Frame(main_frame)
        log_button_frame.grid(row=10, column=2, sticky=tk.E, pady=(0, 5))
        ttk.Button(log_button_frame, text="清除日志", command=self.clear_log).pack()

        # 日志输出区域
        ttk.Label(main_frame, text="日志输出:").grid(row=11, column=0, sticky=(tk.W, tk.N), pady=5)

        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=11, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=60)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        main_frame.rowconfigure(11, weight=1)
    
    def log_message(self, message):
        """输出日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在GUI中显示
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        
        # 同时输出到控制台
        print(log_entry.strip())
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(
            title="选择数据文件夹",
            initialdir=self.data_folder_var.get()
        )
        if folder:
            self.data_folder_var.set(folder)
            self.config["data_folder"] = folder
            self.save_config()
            self.refresh_folder_view()

    def browse_themida(self):
        """浏览Themida.exe文件"""
        file_path = filedialog.askopenfilename(
            title="选择Themida.exe文件",
            initialdir=os.path.dirname(self.themida_path_var.get()) if self.themida_path_var.get() else "",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if file_path:
            self.themida_path_var.set(file_path)
            self.config["themida_path"] = file_path
            self.save_config()
            self.log_message(f"已设置Themida路径: {file_path}")

    def browse_protected_output(self):
        """浏览受保护文件输出目录"""
        folder = filedialog.askdirectory(
            title="选择受保护文件输出目录",
            initialdir=self.protected_output_var.get()
        )
        if folder:
            self.protected_output_var.set(folder)
            self.config["protected_output_dir"] = folder
            self.save_config()
            self.log_message(f"已设置受保护文件输出目录: {folder}")

    def browse_launcher(self):
        """浏览launcher.shaiya文件"""
        file_path = filedialog.askopenfilename(
            title="选择launcher.shaiya文件",
            initialdir=os.path.dirname(self.launcher_path_var.get()) if self.launcher_path_var.get() else "",
            filetypes=[("Shaiya文件", "*.shaiya"), ("所有文件", "*.*")]
        )
        if file_path:
            self.launcher_path_var.set(file_path)
            self.config["launcher_path"] = file_path
            self.save_config()
            self.log_message(f"已设置launcher.shaiya路径: {file_path}")

    def browse_bin_output(self):
        """浏览.bin文件输出目录"""
        folder = filedialog.askdirectory(
            title="选择.bin文件输出目录",
            initialdir=self.bin_output_var.get()
        )
        if folder:
            self.bin_output_var.set(folder)
            self.config["bin_output_dir"] = folder
            self.save_config()
            self.log_message(f"已设置.bin文件输出目录: {folder}")

    def browse_signed_game(self):
        """浏览签名后的game.exe文件"""
        file_path = filedialog.askopenfilename(
            title="选择签名后的game.exe文件",
            initialdir="",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if file_path:
            self.signed_game_var.set(file_path)
            self.log_message(f"已选择签名文件: {file_path}")
    
    def refresh_folder_view(self):
        """刷新文件夹视图"""
        data_folder = self.data_folder_var.get()
        if not data_folder or not os.path.exists(data_folder):
            self.log_message("数据文件夹不存在")
            return

        self.current_path = data_folder
        self.clear_selection()
        self.populate_folder_list(data_folder)
        self.log_message("文件夹视图已刷新")

    def populate_folder_list(self, path):
        """填充文件夹列表"""
        try:
            # 清空列表
            self.folder_listbox.delete(0, tk.END)

            # 更新路径显示
            self.current_path = path
            self.path_label.config(text=path)

            # 获取文件夹列表
            folders = []
            for item in sorted(os.listdir(path)):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    folders.append(item)

            # 添加到列表框
            for folder in folders:
                self.folder_listbox.insert(tk.END, folder)

            self.log_message(f"已加载 {len(folders)} 个文件夹")

        except PermissionError:
            self.log_message(f"无权限访问: {path}")
        except Exception as e:
            self.log_message(f"加载文件夹失败: {e}")

    def go_up(self):
        """返回上级目录"""
        if self.current_path:
            parent_path = os.path.dirname(self.current_path)
            if parent_path and parent_path != self.current_path:
                self.populate_folder_list(parent_path)
                self.log_message(f"返回上级目录: {os.path.basename(parent_path)}")

    def clear_selection(self):
        """清除所有选择"""
        self.selected_items.clear()
        self.operation_folder = None
        self.technical_folder = None

        # 清除列表框选择
        self.folder_listbox.selection_clear(0, tk.END)

        # 重新设置列表框颜色
        self.update_folder_colors()

        self.log_message("已清除所有选择")

    def update_folder_colors(self):
        """更新文件夹颜色显示"""
        # 重置所有项目的背景色
        for i in range(self.folder_listbox.size()):
            folder_name = self.folder_listbox.get(i)
            if self.operation_folder and os.path.basename(self.operation_folder) == folder_name:
                self.folder_listbox.itemconfig(i, {'bg': 'lightblue'})
            elif self.technical_folder and os.path.basename(self.technical_folder) == folder_name:
                self.folder_listbox.itemconfig(i, {'bg': 'lightgreen'})
            else:
                self.folder_listbox.itemconfig(i, {'bg': 'white'})
    
    def on_folder_single_click(self, event):
        """单击文件夹事件"""
        pass

    def on_folder_double_click(self, event):
        """双击文件夹事件 - 进入文件夹"""
        selection = self.folder_listbox.curselection()
        if not selection:
            return

        folder_name = self.folder_listbox.get(selection[0])
        folder_path = os.path.join(self.current_path, folder_name)

        if os.path.isdir(folder_path):
            self.populate_folder_list(folder_path)
            self.log_message(f"进入文件夹: {folder_name}")

    def select_current_folder(self):
        """选择当前选中的文件夹"""
        selection = self.folder_listbox.curselection()
        if not selection:
            self.log_message("请先在文件夹列表中选择一个文件夹")
            return

        folder_name = self.folder_listbox.get(selection[0])
        folder_path = os.path.join(self.current_path, folder_name)

        if not os.path.isdir(folder_path):
            self.log_message("选中的不是有效文件夹")
            return

        # 检查是否已经选中
        if "运营" in folder_name:
            if self.operation_folder == folder_path:
                # 取消选择
                self.operation_folder = None
                self.log_message(f"已取消选择运营文件夹: {folder_name}")
            else:
                # 选择运营文件夹
                self.operation_folder = folder_path
                self.log_message(f"已选择运营文件夹: {folder_name}")

        elif "技术" in folder_name:
            if self.technical_folder == folder_path:
                # 取消选择
                self.technical_folder = None
                self.log_message(f"已取消选择技术文件夹: {folder_name}")
            else:
                # 选择技术文件夹
                self.technical_folder = folder_path
                self.log_message(f"已选择技术文件夹: {folder_name}")
        else:
            self.log_message(f"警告: 文件夹 '{folder_name}' 不包含'运营'或'技术'关键字")
            return

        # 更新颜色显示
        self.update_folder_colors()

        # 显示当前选择状态
        self.show_selection_status()

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
            self.log_message("日志已清除")

    def process_sfile_packaging(self, data_folder):
        """处理SFile打包"""
        try:
            self.log_message("开始SFile打包处理...")

            # 检查SFile.exe是否存在
            sfile_path = self.find_sfile_exe()
            if not sfile_path:
                self.log_message("错误: 未找到SFile.exe，请确保SFile.exe在当前目录或PATH中")
                return False

            # 创建~patch目录在当前脚本路径下
            current_dir = os.getcwd()
            patch_dir = os.path.join(current_dir, "~patch")
            os.makedirs(patch_dir, exist_ok=True)
            self.log_message(f"创建~patch目录: {patch_dir}")

            # 调用SFile命令行打包
            success = self.call_sfile_command(sfile_path, data_folder, patch_dir)

            if success:
                # 创建patch~目录并移动文件
                self.create_final_patch_folder(patch_dir, current_dir)

            return success

        except Exception as e:
            self.log_message(f"SFile打包处理失败: {e}")
            return False

    def find_sfile_exe(self):
        """查找SFile.exe"""
        # 可能的SFile.exe位置
        possible_paths = [
            "SFile.exe",  # 当前目录
            "SFile\\SFile.exe",  # SFile子目录
            os.path.join(os.getcwd(), "SFile.exe"),  # 完整路径
            os.path.join(os.getcwd(), "SFile", "SFile.exe"),  # SFile子目录完整路径
        ]

        for path in possible_paths:
            if os.path.exists(path):
                self.log_message(f"找到SFile.exe: {path}")
                return path

        # 检查PATH中是否有SFile.exe
        try:
            result = subprocess.run(["where", "SFile.exe"], capture_output=True, text=True)
            if result.returncode == 0:
                path = result.stdout.strip().split('\n')[0]
                self.log_message(f"在PATH中找到SFile.exe: {path}")
                return path
        except:
            pass

        return None

    def call_sfile_command(self, sfile_path, data_folder, output_dir):
        """调用SFile命令行进行打包"""
        try:
            # 根据SFile的实现，我们需要模拟ThreadMakeDataFile的功能
            # 由于原SFile.exe是GUI程序，我们需要创建一个命令行包装

            self.log_message("正在打包data文件夹...")

            # 使用Python实现SFile的核心打包功能
            return self.python_sfile_pack(data_folder, output_dir)

        except Exception as e:
            self.log_message(f"SFile命令调用失败: {e}")
            return False

    def python_sfile_pack(self, data_folder, output_dir):
        """使用Python实现SFile打包功能"""
        try:
            import struct
            import time

            # 输出文件路径
            saf_file = os.path.join(output_dir, "update.saf")
            sah_file = os.path.join(output_dir, "update.sah")

            self.log_message(f"创建SAF文件: {saf_file}")
            self.log_message(f"创建SAH文件: {sah_file}")

            # 收集所有文件
            files_info = []
            total_size = 0
            current_offset = 0

            for root, dirs, files in os.walk(data_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, data_folder)
                    file_size = os.path.getsize(file_path)
                    file_time = int(os.path.getmtime(file_path))

                    files_info.append({
                        'name': file,
                        'path': rel_path,
                        'full_path': file_path,
                        'size': file_size,
                        'time': file_time,
                        'offset': current_offset
                    })

                    current_offset += file_size + 1  # +1 for null terminator
                    total_size += file_size

            self.log_message(f"找到 {len(files_info)} 个文件，总大小: {total_size} 字节")

            # 写入SAF文件（数据文件）
            with open(saf_file, 'wb') as saf:
                for file_info in files_info:
                    with open(file_info['full_path'], 'rb') as f:
                        data = f.read()
                        saf.write(data)
                        saf.write(b'\x00')  # null terminator

            # 写入SAH文件（头文件）
            with open(sah_file, 'wb') as sah:
                # 写入版本和文件数量
                version = 1
                file_count = len(files_info)

                sah.write(struct.pack('<I', version))  # 版本号
                sah.write(struct.pack('<I', file_count))  # 文件数量

                # 写入根目录信息
                self.write_directory_header(sah, data_folder, files_info)

                # 写入空列表数量（0）
                sah.write(struct.pack('<I', 0))

            self.log_message("SFile打包完成")
            return True

        except Exception as e:
            self.log_message(f"Python SFile打包失败: {e}")
            return False

    def write_directory_header(self, sah_file, base_path, files_info):
        """写入目录头信息"""
        import struct

        # 构建目录树
        dir_tree = {}

        for file_info in files_info:
            path_parts = file_info['path'].replace('\\', '/').split('/')
            current_dir = dir_tree

            # 创建目录结构
            for i, part in enumerate(path_parts[:-1]):  # 除了文件名的所有部分
                if part not in current_dir:
                    current_dir[part] = {'dirs': {}, 'files': []}
                current_dir = current_dir[part]['dirs']

            # 添加文件到最后一个目录
            if len(path_parts) == 1:
                # 文件在根目录
                if 'files' not in dir_tree:
                    dir_tree['files'] = []
                dir_tree['files'].append(file_info)
            else:
                # 文件在子目录
                if 'files' not in current_dir:
                    current_dir['files'] = []
                current_dir['files'].append(file_info)

        # 写入根目录
        self.write_dir_recursive(sah_file, "", dir_tree, files_info)

    def write_dir_recursive(self, sah_file, dir_name, dir_data, all_files):
        """递归写入目录信息"""
        import struct

        # 写入目录名
        dir_name_bytes = dir_name.encode('utf-8') + b'\x00'
        sah_file.write(struct.pack('<I', len(dir_name_bytes)))
        sah_file.write(dir_name_bytes)

        # 写入文件信息
        files = dir_data.get('files', [])
        sah_file.write(struct.pack('<I', len(files)))

        for file_info in files:
            # 文件名
            name_bytes = file_info['name'].encode('utf-8') + b'\x00'
            sah_file.write(struct.pack('<I', len(name_bytes)))
            sah_file.write(name_bytes)

            # 偏移量、大小、时间
            sah_file.write(struct.pack('<I', file_info['offset']))  # 偏移量
            sah_file.write(struct.pack('<I', file_info['size']))    # 大小
            sah_file.write(struct.pack('<I', file_info['time']))    # 时间

        # 写入子目录信息
        subdirs = dir_data.get('dirs', {})
        sah_file.write(struct.pack('<I', len(subdirs)))

        for subdir_name, subdir_data in subdirs.items():
            self.write_dir_recursive(sah_file, subdir_name, subdir_data, all_files)

    def create_final_patch_folder(self, patch_dir, current_dir):
        """创建最终的patch~文件夹"""
        try:
            # 创建patch~目录在当前脚本路径下
            final_patch_dir = os.path.join(current_dir, "patch~")
            os.makedirs(final_patch_dir, exist_ok=True)
            self.log_message(f"创建patch~目录: {final_patch_dir}")

            # 移动update.saf和update.sah到patch~目录
            saf_src = os.path.join(patch_dir, "update.saf")
            sah_src = os.path.join(patch_dir, "update.sah")
            saf_dst = os.path.join(final_patch_dir, "update.saf")
            sah_dst = os.path.join(final_patch_dir, "update.sah")

            if os.path.exists(saf_src):
                if os.path.exists(saf_dst):
                    os.remove(saf_dst)
                os.rename(saf_src, saf_dst)
                self.log_message(f"移动update.saf到patch~目录")

            if os.path.exists(sah_src):
                if os.path.exists(sah_dst):
                    os.remove(sah_dst)
                os.rename(sah_src, sah_dst)
                self.log_message(f"移动update.sah到patch~目录")

            # 删除临时~patch目录
            try:
                os.rmdir(patch_dir)
                self.log_message("清理临时~patch目录")
            except:
                pass

            self.log_message("运营文件夹SFile打包处理完成!")
            self.log_message(f"最终文件位置: {final_patch_dir}")
            return True

        except Exception as e:
            self.log_message(f"创建patch~文件夹失败: {e}")
            return False

    def process_themida_protection(self, game_exe_path):
        """处理Themida加壳"""
        try:
            self.log_message("开始Themida加壳处理...")

            # 查找Themida.exe
            themida_path = self.find_themida_exe()
            if not themida_path:
                self.log_message("错误: 未找到Themida.exe")
                return False

            # 创建项目文件
            project_file = self.create_themida_project(game_exe_path)
            if not project_file:
                self.log_message("创建Themida项目文件失败")
                return False

            # 执行加壳
            success = self.execute_themida_protection(themida_path, project_file, game_exe_path)

            if success:
                # 处理加壳后的文件
                self.handle_protected_file(game_exe_path)

                            # 清理临时文件
            return success

        except Exception as e:
            self.log_message(f"Themida加壳处理失败: {e}")
            return False

    def find_themida_exe(self):
        """查找Themida.exe"""
        # 从配置文件获取Themida路径
        themida_path = self.config.get("themida_path", "")

        if themida_path and os.path.exists(themida_path):
            self.log_message(f"找到Themida.exe: {themida_path}")
            return themida_path

        # 如果配置的路径不存在，尝试默认路径
        default_paths = [
            r"f:\Themida_x32_x64_v3.1.4.18_Retail_Licensed\Themida.exe"
        ]

        for path in default_paths:
            if os.path.exists(path):
                self.log_message(f"找到Themida.exe: {path}")
                # 更新配置
                self.config["themida_path"] = path
                self.themida_path_var.set(path)
                self.save_config()
                return path

        self.log_message("错误: 未找到Themida.exe，请在界面中配置正确的路径")
        return None

    def create_themida_project(self, game_exe_path):
        """创建Themida项目文件"""
        try:
            # 生成输出文件路径
            protected_output_dir = self.config.get("protected_output_dir", "")
            if protected_output_dir and os.path.exists(protected_output_dir):
                # 使用配置的输出目录
                protected_exe = os.path.join(protected_output_dir, "game_protected.exe")
            else:
                # 使用game.exe所在目录
                game_dir = os.path.dirname(game_exe_path)
                protected_exe = os.path.join(game_dir, "game_protected.exe")

            # 创建临时项目文件，使用.tmd扩展名（WinLicense项目文件）
            project_file = os.path.join(os.getcwd(), "temp_project_file.tmd")

            # 标准的WinLicense/Themida项目文件格式
            # 先处理路径，避免在f-string中使用反斜杠
            game_exe_path = game_exe_path.replace('/', '\\')
            protected_exe = protected_exe.replace('/', '\\')

            project_content = f"""
[SECURE_ENGINE]
INPUT_FILE_NAME={game_exe_path}
OUTPUT_FILE_NAME={protected_exe}
SOFTWARE_NAME=Shaiya
OPTION_XBUNDLER_NUMBER_FILES=0
OPTION_PLUGINS_NUMBER_FILES=0
ADVANCED_OPTIONS_NUMBER=0
OPTION_MACROS_MAP_FUNCTIONS_NUMBER=0
OPTION_MACROS_DISABLE_AT_RVA_NUMBER=0
OPTION_VIRTUAL_MACHINE_NUMBER=1
OPTION_VIRTUAL_MACHINE_ID_AT_0=0x3E344705
OPTION_VIRTUAL_MACHINE_CPUS_AT_0=1
OPTION_VIRTUAL_MACHINE_PROTECTION_BOOT_ID=0x3E344705
OPTION_VIRTUAL_MACHINE_OLD_VM_MACROS_ID=0x3E344705
MSG_ID_GLOBAL_CAPTION_ICON=INFORMATION
MSG_ID_GLOBAL_CAPTION_IS_ENABLED=true
MSG_ID_GLOBAL_CAPTION_NUMBER_LANGUAGES=1
MSG_ID_GLOBAL_CAPTION_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_GLOBAL_CAPTION_LANGUAGE_0_TEXT=Themida
MSG_ID_DEBUGGER_ICON=ERROR
MSG_ID_DEBUGGER_IS_ENABLED=true
MSG_ID_DEBUGGER_NUMBER_LANGUAGES=1
MSG_ID_DEBUGGER_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_DEBUGGER_LANGUAGE_0_TEXT=A debugger has been found running in your system.\nPlease, unload it from memory and restart your program
MSG_ID_MONITOR_ICON=ERROR
MSG_ID_MONITOR_IS_ENABLED=true
MSG_ID_MONITOR_NUMBER_LANGUAGES=1
MSG_ID_MONITOR_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_MONITOR_LANGUAGE_0_TEXT=A monitor program has been found running in your system.\nPlease, unload it from memory and restart your program
MSG_ID_CRACK_TOOL_ICON=ERROR
MSG_ID_CRACK_TOOL_IS_ENABLED=true
MSG_ID_CRACK_TOOL_NUMBER_LANGUAGES=1
MSG_ID_CRACK_TOOL_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_CRACK_TOOL_LANGUAGE_0_TEXT=A cracker's tool has been found running in your system.\nPlease, unload it from memory and restart your program
MSG_ID_FILE_CORRUPTED_ICON=ERROR
MSG_ID_FILE_CORRUPTED_IS_ENABLED=true
MSG_ID_FILE_CORRUPTED_NUMBER_LANGUAGES=1
MSG_ID_FILE_CORRUPTED_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_FILE_CORRUPTED_LANGUAGE_0_TEXT=File corrupted!. This application has been manipulated and maybe\nit's infected by a Virus or cracked. This application won't work anymore
MSG_ID_EXCEPTION_ICON=ERROR
MSG_ID_EXCEPTION_IS_ENABLED=true
MSG_ID_EXCEPTION_NUMBER_LANGUAGES=1
MSG_ID_EXCEPTION_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_EXCEPTION_LANGUAGE_0_TEXT=An internal exception occurred (Address: 0x%x)\nPlease, contact <EMAIL>. Thank you!
MSG_ID_VIRTUAL_PC_ICON=ERROR
MSG_ID_VIRTUAL_PC_IS_ENABLED=true
MSG_ID_VIRTUAL_PC_NUMBER_LANGUAGES=1
MSG_ID_VIRTUAL_PC_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_VIRTUAL_PC_LANGUAGE_0_TEXT=Sorry, this application cannot run under a Virtual Machine
MSG_ID_MISSING_DLL_ICON=ERROR
MSG_ID_MISSING_DLL_IS_ENABLED=true
MSG_ID_MISSING_DLL_NUMBER_LANGUAGES=1
MSG_ID_MISSING_DLL_LANGUAGE_0_ID=[DEFAULT]
MSG_ID_MISSING_DLL_LANGUAGE_0_TEXT=The DLL "%s" cannot be loaded
OPTION_PROTECTION_IS_ANTIDEBUG=true
OPTION_PROTECTION_IS_API_WRAPPER_ENABLED=true
OPTION_COMPRESSION_COMPRESS_APPLICATION=true
OPTION_COMPRESSION_COMPRESS_RESOURCES=true
OPTION_COMPRESSION_COMPRESS_SECUREENGINE=true
OPTION_MACROS_ENCRYPT_ANSI_STRINGS=false
OPTION_MACROS_ENCRYPT_UNICODE_STRINGS=false
OPTION_MACROS_STRING_REENCRYPT=false
OPTION_EXTRA_IS_MANIFEST_FILE_ENABLED=false
OPTION_EXTRA_MANIFEST_FILE=
OPTION_EXTRA_IS_MANIFEST_FROM_XBUNDLER=false
OPTION_PROTECTION_IS_FILE_REGISTRY_MONITORS=true
OPTION_PROTECTION_IS_ENTRYPOINT_OBFUSCATION=false
OPTION_PROTECTION_IS_ANTIDEBUG=true
OPTION_PROTECTION_IS_ANTIPATCHING=false
OPTION_PROTECTION_IS_SANDBOX_MONITORS=false
OPTION_PROTECTION_IS_VMWARE_SUPPORT=true
OPTION_MACROS_INTEGRITY_CHECKS=true
OPTION_PROTECTION_IS_SPEED_OVER_PROTECTION=false
OPTION_PROTECTION_IS_SIZE_OVER_PROTECTION=false
OPTION_PROTECTION_IS_OPTIMIZE_WINDOWS_ARM=false
OPTION_EXTRA_IS_SPLASH=false
OPTION_EXTRA_SPLASH_FILENAME=
OPTION_EXTRA_SPLASH_DISPLAY_SECONDS=0
OPTION_EXTRA_SPLASH_CLOSE_ON_CLICK=true
OPTION_EXTRA_SPLASH_CLOSE_AFTER_SECONDS=false
OPTION_EXTRA_SPLASH_CLOSE_BY_SDK=false
OPTION_PLUGINS_PROCESS_HOOKING=false
OPTION_XBUNDLER_IS_ENABLED=false
OPTION_XBUNDLER_IS_DELETE_ON_EXIT=false
OPTION_XBUNDLER_HOOK_FIND_NEXT_FILE=false
OPTION_XBUNDLER_MAXIMIZE_SPEED=false
OPTION_XBUNDLER_IS_ACTIVEX_SUPPORT=false
OPTION_XBUNDLER_IS_SUPPORT_GETPRIVATEPROFILE=false
OPTION_XBUNDLER_EXCEPTION_SUPPORT=false
"""
            with open(project_file, 'w', encoding='utf-8') as f:
                f.write(project_content)

            self.log_message(f"创建Themida项目文件: {project_file}")
            self.log_message(f"输入文件: {game_exe_path}")
            self.log_message(f"输出文件: {protected_exe}")

            return project_file

        except Exception as e:
            self.log_message(f"创建Themida项目文件失败: {e}")
            return None

    def execute_themida_protection(self, themida_path, project_file, game_exe_path):
        """执行Themida加壳"""
        try:
            # 生成输出文件路径
            protected_output_dir = self.config.get("protected_output_dir", "")
            if protected_output_dir and os.path.exists(protected_output_dir):
                # 使用配置的输出目录
                protected_exe = os.path.join(protected_output_dir, "game_protected.exe")
            else:
                # 使用game.exe所在目录
                game_dir = os.path.dirname(game_exe_path)
                protected_exe = os.path.join(game_dir, "game_protected.exe")

            project_file = project_file.replace('\\', '/')
            game_exe_path = game_exe_path.replace('\\', '/')
            protected_exe = protected_exe.replace('\\', '/')
            # 根据帮助信息，使用正确的命令格式
            # usage: WinLicense /protect ProjectFileName [options]
            cmd = [
                themida_path,
                "/protect", project_file,
                "/inputfile", game_exe_path,
                "/outputfile", protected_exe,
                 "/q"  # 静默模式
                # "/shareconsole"  # 避免附加到当前控制台
            ]

            self.log_message("开始执行Themida加壳...")
            self.log_message(f"命令: {' '.join(cmd)}")

            # 配置启动信息以隐藏窗口
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            # 执行命令，设置工作目录并隐藏窗口
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,
                cwd=os.path.dirname(themida_path),  # 在Themida目录下执行
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if result.returncode == 0:
                if os.path.exists(protected_exe):
                    self.log_message("Themida加壳成功完成")
                    self.log_message(f"生成保护文件: {protected_exe}")
                    return True
                else:
                    self.log_message("加壳命令执行成功，但未找到输出文件")
                    return False
            else:
                self.log_message(f"Themida加壳失败")
                if result.stdout:
                    self.log_message(f"输出信息: {result.stdout}")
                if result.stderr:
                    self.log_message(f"错误信息: {result.stderr}")

                return False

        except subprocess.TimeoutExpired:
            self.log_message("Themida加壳超时")
            return False
        except Exception as e:
            self.log_message(f"执行Themida加壳失败: {e}")
            return False

    def handle_protected_file(self, original_game_exe):
        """处理加壳后的文件"""
        try:
            # 确定受保护文件的位置
            protected_output_dir = self.config.get("protected_output_dir", "")
            if protected_output_dir and os.path.exists(protected_output_dir):
                # 使用配置的输出目录
                protected_exe = os.path.join(protected_output_dir, "game_protected.exe")
                output_location = protected_output_dir
            else:
                # 使用game.exe所在目录
                game_dir = os.path.dirname(original_game_exe)
                protected_exe = os.path.join(game_dir, "game_protected.exe")
                output_location = game_dir

            if not os.path.exists(protected_exe):
                self.log_message("未找到加壳后的文件")
                return False

            self.log_message("技术文件夹Themida加壳处理完成!")
            self.log_message(f"受保护的文件位置: {protected_exe}")
            self.log_message("注意: 请将game_protected.exe发送给SP小组进行签名")

            return True

        except Exception as e:
            self.log_message(f"处理加壳文件失败: {e}")
            return False

    def show_selection_status(self):
        """显示当前选择状态"""
        status_parts = []
        if self.operation_folder:
            status_parts.append(f"运营: {os.path.basename(self.operation_folder)}")
        if self.technical_folder:
            status_parts.append(f"技术: {os.path.basename(self.technical_folder)}")

        if status_parts:
            status = "当前选择: " + " | ".join(status_parts)
        else:
            status = "当前选择: 无"

        self.log_message(status)

    def start_processing(self):
        """开始签名前处理"""
        if not self.operation_folder and not self.technical_folder:
            messagebox.showwarning("警告", "请先选择运营或技术文件夹")
            return
        
        # 在新线程中执行处理
        thread = threading.Thread(target=self.process_folders)
        thread.daemon = True
        thread.start()
    
    def process_folders(self):
        """处理文件夹"""
        try:
            self.log_message("开始签名前处理...")
            
            if self.operation_folder:
                self.log_message(f"处理运营文件夹: {self.operation_folder}")
                self.process_operation_folder(self.operation_folder)
            
            if self.technical_folder:
                self.log_message(f"处理技术文件夹: {self.technical_folder}")
                self.process_technical_folder(self.technical_folder)
            
            self.log_message("签名前处理完成!")
            
        except Exception as e:
            self.log_message(f"处理过程中发生错误: {e}")
    
    def extract_archive(self, archive_path, extract_to):
        """解压文件到指定目录"""
        try:
            # 确保解压目录存在
            os.makedirs(extract_to, exist_ok=True)

            if archive_path.lower().endswith('.zip'):
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
                self.log_message(f"已解压ZIP文件到: {extract_to}")
                return True

            elif archive_path.lower().endswith('.rar'):
                # 尝试多种RAR解压方法
                return self.extract_rar_file(archive_path, extract_to)
            else:
                self.log_message(f"不支持的压缩格式: {archive_path}")
                return False

        except Exception as e:
            self.log_message(f"解压失败 {archive_path}: {e}")
            return False

    def extract_rar_file(self, rar_path, extract_to):
        """解压RAR文件，尝试多种方法"""
        # 方法1: 使用rarfile库
        if RARFILE_AVAILABLE:
            try:
                # 尝试设置RAR工具路径
                possible_rar_paths = [
                    r"C:\Program Files\WinRAR\UnRAR.exe",
                    r"C:\Program Files (x86)\WinRAR\UnRAR.exe",
                    r"C:\Program Files\WinRAR\Rar.exe",
                    r"C:\Program Files (x86)\WinRAR\Rar.exe",
                    "unrar",  # 如果在PATH中
                    "rar"     # 如果在PATH中
                ]

                for rar_tool in possible_rar_paths:
                    if os.path.exists(rar_tool) or rar_tool in ["unrar", "rar"]:
                        try:
                            rarfile.UNRAR_TOOL = rar_tool
                            with rarfile.RarFile(rar_path, 'r') as rar_ref:
                                rar_ref.extractall(extract_to)
                            self.log_message(f"已解压RAR文件到: {extract_to} (使用 {rar_tool})")
                            return True
                        except Exception as e:
                            self.log_message(f"使用 {rar_tool} 解压失败: {e}")
                            continue
            except Exception as e:
                self.log_message(f"rarfile库解压失败: {e}")

        # 方法2: 使用命令行工具
        return self.extract_rar_with_command(rar_path, extract_to)

    def extract_rar_with_command(self, rar_path, extract_to):
        """使用命令行工具解压RAR"""
        commands_to_try = [
            # WinRAR命令行
            [r"C:\Program Files\WinRAR\UnRAR.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files (x86)\WinRAR\UnRAR.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files\WinRAR\Rar.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files (x86)\WinRAR\Rar.exe", "x", "-y", rar_path, extract_to + "\\"],
            # 7-Zip命令行
            [r"C:\Program Files\7-Zip\7z.exe", "x", rar_path, f"-o{extract_to}", "-y"],
            [r"C:\Program Files (x86)\7-Zip\7z.exe", "x", rar_path, f"-o{extract_to}", "-y"],
            # 系统PATH中的工具
            ["unrar", "x", "-y", rar_path, extract_to + "\\"],
            ["rar", "x", "-y", rar_path, extract_to + "\\"],
            ["7z", "x", rar_path, f"-o{extract_to}", "-y"]
        ]

        for cmd in commands_to_try:
            try:
                # 检查工具是否存在
                if not os.path.exists(cmd[0]) and cmd[0] not in ["unrar", "rar", "7z"]:
                    continue

                self.log_message(f"尝试使用命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    self.log_message(f"已解压RAR文件到: {extract_to} (使用 {cmd[0]})")
                    return True
                else:
                    self.log_message(f"命令执行失败 ({cmd[0]}): {result.stderr}")

            except subprocess.TimeoutExpired:
                self.log_message(f"解压超时: {cmd[0]}")
            except FileNotFoundError:
                self.log_message(f"工具未找到: {cmd[0]}")
            except Exception as e:
                self.log_message(f"命令执行异常 ({cmd[0]}): {e}")

        # 如果所有方法都失败，提供手动解压建议
        self.log_message("自动解压RAR失败，请考虑以下解决方案：")
        self.log_message("1. 安装WinRAR或7-Zip软件")
        self.log_message("2. 手动解压RAR文件到指定目录")
        self.log_message("3. 将RAR文件转换为ZIP格式")
        return False
    
    def find_data_folder(self, base_path):
        """查找data文件夹"""
        for root, dirs, files in os.walk(base_path):
            if 'data' in [d.lower() for d in dirs]:
                data_path = os.path.join(root, 'data')
                self.log_message(f"找到data文件夹: {data_path}")
                return data_path
        return None
    
    def find_game_exe(self, base_path):
        """查找game.exe文件"""
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.lower() == 'game.exe':
                    game_path = os.path.join(root, file)
                    self.log_message(f"找到game.exe: {game_path}")
                    return game_path
        return None
    
    def get_archive_name_without_extension(self, archive_path):
        """获取压缩文件名（不含扩展名）"""
        filename = os.path.basename(archive_path)
        # 移除.zip, .rar等扩展名
        for ext in ['.zip', '.rar', '.7z']:
            if filename.lower().endswith(ext):
                return filename[:-len(ext)]
        return filename

    def is_already_extracted(self, folder_path, archive_path):
        """检查是否已经解压过"""
        archive_name = self.get_archive_name_without_extension(archive_path)

        # 检查是否存在同名文件夹
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isdir(item_path) and item == archive_name:
                self.log_message(f"检测到已解压的文件夹: {item}")
                return True, item_path

        return False, None

    def process_operation_folder(self, folder_path):
        """处理运营文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                self.log_message(f"发现压缩文件: {file}")

                # 检查是否已经解压过
                already_extracted, extracted_path = self.is_already_extracted(folder_path, file_path)

                if already_extracted:
                    self.log_message(f"文件已解压，跳过解压步骤")
                    search_path = extracted_path
                else:
                    # 直接解压到当前文件夹
                    if self.extract_archive(file_path, folder_path):
                        # 获取解压后的文件夹路径
                        archive_name = self.get_archive_name_without_extension(file_path)
                        search_path = os.path.join(folder_path, archive_name)
                        if not os.path.exists(search_path):
                            # 如果没有创建同名文件夹，直接在当前文件夹搜索
                            search_path = folder_path
                    else:
                        continue

                # 查找data文件夹
                data_folder = self.find_data_folder(search_path)
                if data_folder:
                    self.log_message(f"运营文件夹处理完成，data文件夹位于: {data_folder}")
                    # 调用SFile.exe进行打包
                    self.process_sfile_packaging(data_folder)
                else:
                    self.log_message("未找到data文件夹")

    def process_technical_folder(self, folder_path):
        """处理技术文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                self.log_message(f"发现压缩文件: {file}")

                # 检查是否已经解压过
                already_extracted, extracted_path = self.is_already_extracted(folder_path, file_path)

                if already_extracted:
                    self.log_message(f"文件已解压，跳过解压步骤")
                    search_path = extracted_path
                else:
                    # 直接解压到当前文件夹
                    if self.extract_archive(file_path, folder_path):
                        # 获取解压后的文件夹路径
                        archive_name = self.get_archive_name_without_extension(file_path)
                        search_path = os.path.join(folder_path, archive_name)
                        if not os.path.exists(search_path):
                            # 如果没有创建同名文件夹，直接在当前文件夹搜索
                            search_path = folder_path
                    else:
                        continue

                # 查找game.exe
                game_exe = self.find_game_exe(search_path)
                if game_exe:
                    self.log_message(f"技术文件夹处理完成，game.exe位于: {game_exe}")
                    # 进行Themida加壳处理
                    self.process_themida_protection(game_exe)
                else:
                    self.log_message("未找到game.exe文件")

    def start_post_sign_processing(self):
        """开始签名后处理"""
        signed_game_path = self.signed_game_var.get()
        if not signed_game_path or not os.path.exists(signed_game_path):
            messagebox.showwarning("警告", "请先选择签名后的game.exe文件")
            return

        # 在新线程中执行处理
        thread = threading.Thread(target=self.process_post_sign, args=(signed_game_path,))
        thread.daemon = True
        thread.start()

    def process_post_sign(self, signed_game_path):
        """处理签名后的操作"""
        try:
            self.log_message("开始签名后处理...")

            # 1. 检查patch~文件夹是否存在
            current_dir = os.getcwd()
            patch_dir = os.path.join(current_dir, "patch~")

            if not os.path.exists(patch_dir):
                self.log_message("错误: 未找到patch~文件夹，请先完成签名前处理")
                return False

            # 2. 将签名后的game.exe重命名为game.exe并复制到patch~文件夹
            game_dest = os.path.join(patch_dir, "game.exe")
            if os.path.exists(game_dest):
                os.remove(game_dest)

            import shutil
            shutil.copy2(signed_game_path, game_dest)
            self.log_message(f"已将签名文件复制到patch~文件夹: {game_dest}")

            # 3. 压缩patch~文件夹为zip
            zip_path = os.path.join(current_dir, "patch~.zip")
            if os.path.exists(zip_path):
                os.remove(zip_path)

            self.create_zip_file(patch_dir, zip_path)
            self.log_message(f"已创建压缩文件: {zip_path}")

            # 4. 计算MD5值
            md5_value = self.calculate_md5(zip_path)
            if not md5_value:
                self.log_message("错误: MD5计算失败")
                return False

            self.log_message(f"计算得到MD5值: {md5_value}")

            # 5. 更新launcher.shaiya文件
            if not self.update_launcher_file(md5_value):
                self.log_message("错误: 更新launcher.shaiya文件失败")
                return False

            # 6. 重命名zip文件为MD5值.bin
            bin_output_dir = self.config.get("bin_output_dir", current_dir)
            bin_file_path = os.path.join(bin_output_dir, f"{md5_value}.bin")

            if os.path.exists(bin_file_path):
                os.remove(bin_file_path)

            shutil.move(zip_path, bin_file_path)
            self.log_message(f"已生成最终文件: {bin_file_path}")

            self.log_message("签名后处理完成!")
            return True

        except Exception as e:
            self.log_message(f"签名后处理失败: {e}")
            return False

    def create_zip_file(self, source_dir, zip_path):
        """创建zip压缩文件"""
        try:
            import zipfile
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, source_dir)
                        zipf.write(file_path, arcname)
            return True
        except Exception as e:
            self.log_message(f"创建zip文件失败: {e}")
            return False

    def calculate_md5(self, file_path):
        """计算文件MD5值，使用与MD5.exe相同的算法"""
        try:
            # 使用Python实现MD5计算，模拟MD5.exe的行为
            import hashlib

            md5_hash = hashlib.md5()
            with open(file_path, 'rb') as f:
                # 分块读取文件以处理大文件
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)

            # 添加密钥（与MD5.exe中的MD5_Key="wow"保持一致）
            md5_hash.update(b"wow")

            return md5_hash.hexdigest()

        except Exception as e:
            self.log_message(f"计算MD5失败: {e}")
            return None

    def update_launcher_file(self, md5_value):
        """更新launcher.shaiya文件"""
        try:
            launcher_path = self.config.get("launcher_path", "")
            if not launcher_path or not os.path.exists(launcher_path):
                self.log_message("错误: launcher.shaiya文件路径未配置或文件不存在")
                return False

            # 读取文件内容
            with open(launcher_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 查找[Patch]段和Version行
            patch_section_found = False
            version_line_index = -1
            current_version = None

            for i, line in enumerate(lines):
                line_stripped = line.strip()
                if line_stripped == '[Patch]':
                    patch_section_found = True
                elif patch_section_found and line_stripped.startswith('Version='):
                    version_line_index = i
                    # 提取版本号
                    version_str = line_stripped.split('=')[1]
                    if version_str.startswith('0x'):
                        current_version = int(version_str, 16)
                    else:
                        current_version = int(version_str)
                    break
                elif patch_section_found and line_stripped.startswith('[') and line_stripped != '[Patch]':
                    # 进入了下一个段，停止搜索
                    break

            if not patch_section_found:
                self.log_message("错误: 在launcher.shaiya中未找到[Patch]段")
                return False

            if version_line_index == -1 or current_version is None:
                self.log_message("错误: 在[Patch]段中未找到Version行")
                return False

            # 版本号+1
            new_version = current_version + 1
            new_version_hex = f"0x{new_version:08x}"

            # 更新Version行
            lines[version_line_index] = f"Version={new_version_hex}\n"

            # 在Version行后添加新的MD5行
            new_md5_line = f"{new_version_hex}={md5_value}\n"
            lines.insert(version_line_index + 1, new_md5_line)

            # 写回文件
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            self.log_message(f"已更新launcher.shaiya:")
            self.log_message(f"  版本号: {hex(current_version)} -> {new_version_hex}")
            self.log_message(f"  添加行: {new_md5_line.strip()}")

            return True

        except Exception as e:
            self.log_message(f"更新launcher.shaiya文件失败: {e}")
            return False

def main():
    root = tk.Tk()
    app = MaintenanceGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
