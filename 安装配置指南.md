# 运维流程处理工具 - 安装配置指南

## 解压工具配置

为了支持RAR文件解压，您需要安装以下工具之一：

### 方案1：安装WinRAR（推荐）
1. 下载并安装WinRAR：https://www.win-rar.com/
2. 安装后工具会自动检测以下路径：
   - `C:\Program Files\WinRAR\UnRAR.exe`
   - `C:\Program Files\WinRAR\Rar.exe`
   - `C:\Program Files (x86)\WinRAR\UnRAR.exe`
   - `C:\Program Files (x86)\WinRAR\Rar.exe`

### 方案2：安装7-Zip
1. 下载并安装7-Zip：https://www.7-zip.org/
2. 安装后工具会自动检测以下路径：
   - `C:\Program Files\7-Zip\7z.exe`
   - `C:\Program Files (x86)\7-Zip\7z.exe`

### 方案3：使用rarfile Python库
如果您不想安装额外软件，可以使用Python库：
```bash
pip install rarfile
```
然后下载UnRAR工具并添加到系统PATH中。

## 运行程序

1. 确保已安装Python 3.6+
2. 运行程序：
   ```bash
   python maintenance_gui.py
   ```

## 故障排除

### RAR解压失败
如果遇到"Cannot find working tool"错误：

1. **检查是否安装了解压工具**
   - 确认WinRAR或7-Zip已正确安装
   - 检查安装路径是否正确

2. **手动指定工具路径**
   - 如果工具安装在非标准路径，程序会尝试多个常见位置
   - 查看日志输出了解具体尝试了哪些路径

3. **使用ZIP格式**
   - 如果RAR解压持续失败，建议将文件转换为ZIP格式
   - ZIP格式解压更稳定，无需额外工具

4. **手动解压**
   - 如果自动解压失败，可以手动解压文件
   - 程序会提供解压目标路径信息

### 权限问题
如果遇到权限错误：
- 以管理员身份运行程序
- 确保对目标文件夹有读写权限

### 路径问题
- 避免使用包含特殊字符的路径
- 确保路径长度不超过Windows限制

## 日志信息

程序会详细记录每个操作步骤：
- 文件夹选择
- 解压过程
- 文件查找结果
- 错误信息和建议

查看日志输出区域可以了解处理进度和问题原因。
