/******************************************************************************
 * Header: SecureEngineCustomVMs_GNU_inline.h
 * Description: GNU C inline assembly macros definitions
 *
 * Author/s: Oreans Technologies 
 * (c) 2021 Oreans Technologies
 *
 * --- File generated automatically from Oreans VM Generator (14/5/2021) ---
 ******************************************************************************/

/***********************************************
 * Definition as inline assembly
 ***********************************************/

#ifdef PLATFORM_X32

#ifndef VM_TIGER_WHITE_START
#define VM_TIGER_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x64\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_WHITE_END
#define VM_TIGER_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF4\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_RED_START
#define VM_TIGER_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x65\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_RED_END
#define VM_TIGER_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF5\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_BLACK_START
#define VM_TIGER_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x66\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_BLACK_END
#define VM_TIGER_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF6\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_WHITE_START
#define VM_FISH_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6A\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_WHITE_END
#define VM_FISH_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFA\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_RED_START
#define VM_FISH_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6C\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_RED_END
#define VM_FISH_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFC\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_BLACK_START
#define VM_FISH_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6E\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_BLACK_END
#define VM_FISH_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFE\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_WHITE_START
#define VM_PUMA_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x70\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_WHITE_END
#define VM_PUMA_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x00\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_RED_START
#define VM_PUMA_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x72\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_RED_END
#define VM_PUMA_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x02\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_BLACK_START
#define VM_PUMA_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x74\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_BLACK_END
#define VM_PUMA_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x04\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_WHITE_START
#define VM_SHARK_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x76\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_WHITE_END
#define VM_SHARK_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x06\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_RED_START
#define VM_SHARK_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x78\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_RED_END
#define VM_SHARK_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x08\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_BLACK_START
#define VM_SHARK_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x7A\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_BLACK_END
#define VM_SHARK_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x0A\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_WHITE_START
#define VM_DOLPHIN_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x86\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_WHITE_END
#define VM_DOLPHIN_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x16\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_RED_START
#define VM_DOLPHIN_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x88\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_RED_END
#define VM_DOLPHIN_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x18\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_BLACK_START
#define VM_DOLPHIN_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x8A\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_BLACK_END
#define VM_DOLPHIN_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x1A\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_WHITE_START
#define VM_EAGLE_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x92\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_WHITE_END
#define VM_EAGLE_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x22\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_RED_START
#define VM_EAGLE_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x94\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_RED_END
#define VM_EAGLE_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x24\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_BLACK_START
#define VM_EAGLE_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x96\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_BLACK_END
#define VM_EAGLE_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x26\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_WHITE_START
#define VM_LION_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA0\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_WHITE_END
#define VM_LION_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x30\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_RED_START
#define VM_LION_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA2\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_RED_END
#define VM_LION_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x32\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_BLACK_START
#define VM_LION_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA4\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_BLACK_END
#define VM_LION_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x34\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_MUTATE_ONLY_START
#define VM_MUTATE_ONLY_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x10\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_MUTATE_ONLY_END
#define VM_MUTATE_ONLY_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x11\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#endif

#ifdef PLATFORM_X64

#ifndef VM_TIGER_WHITE_START
#define VM_TIGER_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x67\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_WHITE_END
#define VM_TIGER_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF7\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_RED_START
#define VM_TIGER_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x68\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_RED_END
#define VM_TIGER_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF8\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_BLACK_START
#define VM_TIGER_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x69\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_TIGER_BLACK_END
#define VM_TIGER_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xF9\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_WHITE_START
#define VM_FISH_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6B\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_WHITE_END
#define VM_FISH_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFB\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_RED_START
#define VM_FISH_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6D\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_RED_END
#define VM_FISH_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFD\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_BLACK_START
#define VM_FISH_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x6F\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_FISH_BLACK_END
#define VM_FISH_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xFF\n"\
     ".byte 0x01\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_WHITE_START
#define VM_PUMA_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x71\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_WHITE_END
#define VM_PUMA_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x01\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_RED_START
#define VM_PUMA_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x73\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_RED_END
#define VM_PUMA_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x03\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_BLACK_START
#define VM_PUMA_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x75\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_PUMA_BLACK_END
#define VM_PUMA_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x05\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_WHITE_START
#define VM_SHARK_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x77\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_WHITE_END
#define VM_SHARK_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x07\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_RED_START
#define VM_SHARK_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x79\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_RED_END
#define VM_SHARK_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x09\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_BLACK_START
#define VM_SHARK_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x7B\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_SHARK_BLACK_END
#define VM_SHARK_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x0B\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_WHITE_START
#define VM_DOLPHIN_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x87\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_WHITE_END
#define VM_DOLPHIN_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x17\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_RED_START
#define VM_DOLPHIN_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x89\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_RED_END
#define VM_DOLPHIN_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x19\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_BLACK_START
#define VM_DOLPHIN_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x8B\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_DOLPHIN_BLACK_END
#define VM_DOLPHIN_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x1B\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_WHITE_START
#define VM_EAGLE_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x93\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_WHITE_END
#define VM_EAGLE_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x23\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_RED_START
#define VM_EAGLE_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x95\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_RED_END
#define VM_EAGLE_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x25\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_BLACK_START
#define VM_EAGLE_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x97\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_EAGLE_BLACK_END
#define VM_EAGLE_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x27\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_WHITE_START
#define VM_LION_WHITE_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA1\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_WHITE_END
#define VM_LION_WHITE_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x31\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_RED_START
#define VM_LION_RED_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA3\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_RED_END
#define VM_LION_RED_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x33\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_BLACK_START
#define VM_LION_BLACK_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0xA5\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_LION_BLACK_END
#define VM_LION_BLACK_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x35\n"\
     ".byte 0x02\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_MUTATE_ONLY_START
#define VM_MUTATE_ONLY_START \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x10\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#ifndef VM_MUTATE_ONLY_END
#define VM_MUTATE_ONLY_END \
__asm__ (".byte 0xEB\n"\
     ".byte 0x10\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n"\
     ".byte 0x11\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x00\n"\
     ".byte 0x57\n"\
     ".byte 0x4C\n"\
     ".byte 0x20\n"\
     ".byte 0x20\n");
#endif

#endif

