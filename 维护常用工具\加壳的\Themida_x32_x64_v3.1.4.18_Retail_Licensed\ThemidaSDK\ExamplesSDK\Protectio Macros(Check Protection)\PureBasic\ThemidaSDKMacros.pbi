; include file to use the Themida macros in PureBasic applications


!macro CODEREPLACE_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro CODEREPLACE_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

!macro ENCODE_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro ENCODE_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

!macro CLEAR_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro CLEAR_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

!macro VM_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro VM_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x0D, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

!macro UNREGISTERED_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro UNREGISTERED_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

!macro REGISTERED_START {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}
!macro REGISTERED_END {DB 0xEB, 0x10, 0x57, 0x4C, 0x20, 0x20, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x4C, 0x20, 0x20}

Macro CHECK_PROTECTION(var, val)
!DB 0xEB, 0x10, 0x67, 0x31, 0xAB, 0x91, 0x7A, 0x71, 0x8B, 0x8A, 0xBD, 0x7A, 0x11, 0xBC, 0x00, 0x00, 0x00, 0x00
!push val
!pop dword [var]
!DB 0xEB, 0x0C, 0xBD, 0x7A, 0x11, 0xBC, 0x7A, 0x71, 0x8B, 0x8A, 0x67, 0x31,0xAB, 0x91
EndMacro

Macro CHECK_CODE_INTEGRITY(var, val)
!DB 0xEB, 0x10, 0x67, 0x31, 0xAB, 0x91, 0x7A, 0x71, 0x8B, 0x8A, 0xBD, 0x7A, 0x11, 0xBC, 0x01, 0x00, 0x00, 0x00
!push val
!pop dword [var]
!DB 0xEB, 0x0C, 0xBD, 0x7A, 0x11, 0xBC, 0x7A, 0x71, 0x8B, 0x8A, 0x67, 0x31,0xAB, 0x91
EndMacro

Macro CHECK_REGISTRATION(var, val)
!DB 0xEB, 0x10, 0x67, 0x31, 0xAB, 0x91, 0x7A, 0x71, 0x8B, 0x8A, 0xBD, 0x7A, 0x11, 0xBC, 0x02, 0x00, 0x00, 0x00
!push val
!pop dword [var]
!DB 0xEB, 0x0C, 0xBD, 0x7A, 0x11, 0xBC, 0x7A, 0x71, 0x8B, 0x8A, 0x67, 0x31,0xAB, 0x91
EndMacro

Macro CHECK_VIRTUAL_PC(var, val)
!DB 0xEB, 0x10, 0x67, 0x31, 0xAB, 0x91, 0x7A, 0x71, 0x8B, 0x8A, 0xBD, 0x7A, 0x11, 0xBC, 0x03, 0x00, 0x00, 0x00
!push val
!pop dword [var]
!DB 0xEB, 0x0C, 0xBD, 0x7A, 0x11, 0xBC, 0x7A, 0x71, 0x8B, 0x8A, 0x67, 0x31,0xAB, 0x91
EndMacro
; IDE Options = PureBasic 4.31 (Windows - x86)
; CursorPosition = 24
; FirstLine = 14
; Folding = -
; EnableXP