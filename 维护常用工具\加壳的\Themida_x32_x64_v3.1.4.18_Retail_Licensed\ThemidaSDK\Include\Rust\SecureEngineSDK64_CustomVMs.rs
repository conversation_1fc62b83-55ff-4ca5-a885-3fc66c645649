// ******************************************************************************
// Header: SecureEngineSDK64_CustomVMs.rs
// Description: Rust macros definitions
//
// Author/s: Oreans Technologies 
// (c) 2021 Oreans Technologies
//
// --- File generated automatically from Oreans VM Generator (14/5/2021) ---
// ******************************************************************************

#[allow(dead_code)]
#[link(name="SecureEngineSDK64", kind="dylib")]
extern {
    #[link_name = "CustomVM00000103_Start"]
    fn VM_TIGER_WHITE_START();

    #[link_name = "CustomVM00000103_End"]
    fn VM_TIGER_WHITE_END();

    #[link_name = "CustomVM00000104_Start"]
    fn VM_TIGER_RED_START();

    #[link_name = "CustomVM00000104_End"]
    fn VM_TIGER_RED_END();

    #[link_name = "CustomVM00000105_Start"]
    fn VM_TIGER_BLACK_START();

    #[link_name = "CustomVM00000105_End"]
    fn VM_TIGER_BLACK_END();

    #[link_name = "CustomVM00000107_Start"]
    fn VM_FISH_WHITE_START();

    #[link_name = "CustomVM00000107_End"]
    fn VM_FISH_WHITE_END();

    #[link_name = "CustomVM00000109_Start"]
    fn VM_FISH_RED_START();

    #[link_name = "CustomVM00000109_End"]
    fn VM_FISH_RED_END();

    #[link_name = "CustomVM00000111_Start"]
    fn VM_FISH_BLACK_START();

    #[link_name = "CustomVM00000111_End"]
    fn VM_FISH_BLACK_END();

    #[link_name = "CustomVM00000113_Start"]
    fn VM_PUMA_WHITE_START();

    #[link_name = "CustomVM00000113_End"]
    fn VM_PUMA_WHITE_END();

    #[link_name = "CustomVM00000115_Start"]
    fn VM_PUMA_RED_START();

    #[link_name = "CustomVM00000115_End"]
    fn VM_PUMA_RED_END();

    #[link_name = "CustomVM00000117_Start"]
    fn VM_PUMA_BLACK_START();

    #[link_name = "CustomVM00000117_End"]
    fn VM_PUMA_BLACK_END();

    #[link_name = "CustomVM00000119_Start"]
    fn VM_SHARK_WHITE_START();

    #[link_name = "CustomVM00000119_End"]
    fn VM_SHARK_WHITE_END();

    #[link_name = "CustomVM00000121_Start"]
    fn VM_SHARK_RED_START();

    #[link_name = "CustomVM00000121_End"]
    fn VM_SHARK_RED_END();

    #[link_name = "CustomVM00000123_Start"]
    fn VM_SHARK_BLACK_START();

    #[link_name = "CustomVM00000123_End"]
    fn VM_SHARK_BLACK_END();

    #[link_name = "CustomVM00000135_Start"]
    fn VM_DOLPHIN_WHITE_START();

    #[link_name = "CustomVM00000135_End"]
    fn VM_DOLPHIN_WHITE_END();

    #[link_name = "CustomVM00000137_Start"]
    fn VM_DOLPHIN_RED_START();

    #[link_name = "CustomVM00000137_End"]
    fn VM_DOLPHIN_RED_END();

    #[link_name = "CustomVM00000139_Start"]
    fn VM_DOLPHIN_BLACK_START();

    #[link_name = "CustomVM00000139_End"]
    fn VM_DOLPHIN_BLACK_END();

    #[link_name = "CustomVM00000147_Start"]
    fn VM_EAGLE_WHITE_START();

    #[link_name = "CustomVM00000147_End"]
    fn VM_EAGLE_WHITE_END();

    #[link_name = "CustomVM00000149_Start"]
    fn VM_EAGLE_RED_START();

    #[link_name = "CustomVM00000149_End"]
    fn VM_EAGLE_RED_END();

    #[link_name = "CustomVM00000151_Start"]
    fn VM_EAGLE_BLACK_START();

    #[link_name = "CustomVM00000151_End"]
    fn VM_EAGLE_BLACK_END();

    #[link_name = "CustomVM00000161_Start"]
    fn VM_LION_WHITE_START();

    #[link_name = "CustomVM00000161_End"]
    fn VM_LION_WHITE_END();

    #[link_name = "CustomVM00000163_Start"]
    fn VM_LION_RED_START();

    #[link_name = "CustomVM00000163_End"]
    fn VM_LION_RED_END();

    #[link_name = "CustomVM00000165_Start"]
    fn VM_LION_BLACK_START();

    #[link_name = "CustomVM00000165_End"]
    fn VM_LION_BLACK_END();

    #[link_name = "Mutate_Start"]
    fn VM_MUTATE_ONLY_START();

    #[link_name = "Mutate_End"]
    fn VM_MUTATE_ONLY_END();

}
