// SFileDoc.cpp : implementation of the CSFileDoc class
//

#include "stdafx.h"
#include "SFile.h"

#include "SFileDoc.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSFileDoc

IMPLEMENT_DYNCREATE(CSFileDoc, CDocument)

BEGIN_MESSAGE_MAP(CSFileDoc, CDocument)
	//{{AFX_MSG_MAP(CSFileDoc)
		// NOTE - the ClassWizard will add and remove mapping macros here.
		//    DO NOT EDIT what you see in these blocks of generated code!
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSFileDoc construction/destruction

CSFileDoc::CSFileDoc()
{
	// TODO: add one-time construction code here

}

CSFileDoc::~CSFileDoc()
{
}

BOOL CSFileDoc::OnNewDocument()
{
	if (!CDocument::OnNewDocument())
		return FALSE;

	// TODO: add reinitialization code here
	// (SDI documents will reuse this document)

	return TRUE;
}



/////////////////////////////////////////////////////////////////////////////
// CSFileDoc serialization

void CSFileDoc::Serialize(CArchive& ar)
{
	if (ar.IsStoring())
	{
		// TODO: add storing code here
	}
	else
	{
		// TODO: add loading code here
	}
}

/////////////////////////////////////////////////////////////////////////////
// CSFileDoc diagnostics

#ifdef _DEBUG
void CSFileDoc::AssertValid() const
{
	CDocument::AssertValid();
}

void CSFileDoc::Dump(CDumpContext& dc) const
{
	CDocument::Dump(dc);
}
#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CSFileDoc commands
