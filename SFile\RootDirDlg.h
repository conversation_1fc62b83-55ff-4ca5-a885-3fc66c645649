#if !defined(AFX_ROOTDIRDLG_H__64771308_1D0D_43B7_93A1_19D240CB4698__INCLUDED_)
#define AFX_ROOTDIRDLG_H__64771308_1D0D_43B7_93A1_19D240CB4698__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// RootDirDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CRootDirDlg dialog

class CRootDirDlg : public CDialog
{
// Construction
public:
	CRootDirDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CRootDirDlg)
	enum { IDD = IDD_DLG_ROOT_DIR };
	CString	m_strDir;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CRootDirDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CRootDirDlg)
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ROOTDIRDLG_H__64771308_1D0D_43B7_93A1_19D240CB4698__INCLUDED_)
