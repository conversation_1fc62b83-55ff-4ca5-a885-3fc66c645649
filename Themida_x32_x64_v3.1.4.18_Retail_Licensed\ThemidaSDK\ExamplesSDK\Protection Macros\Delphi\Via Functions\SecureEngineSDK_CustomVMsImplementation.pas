 // --- File generated automatically from Oreans VM Generator (16/6/2015) ---

{$IFDEF WIN64}
  procedure VM_TIGER_WHITE_START; external SecureEngine name 'CustomVM00000103_Start';
  procedure VM_TIGER_WHITE_END; external SecureEngine name 'CustomVM00000103_End';
  procedure VM_TIGER_RED_START; external SecureEngine name 'CustomVM00000104_Start';
  procedure VM_TIGER_RED_END; external SecureEngine name 'CustomVM00000104_End';
  procedure VM_TIGER_BLACK_START; external SecureEngine name 'CustomVM00000105_Start';
  procedure VM_TIGER_BLACK_END; external SecureEngine name 'CustomVM00000105_End';
  procedure VM_FISH_WHITE_START; external SecureEngine name 'CustomVM00000107_Start';
  procedure VM_FISH_WHITE_END; external SecureEngine name 'CustomVM00000107_End';
  procedure VM_FISH_RED_START; external SecureEngine name 'CustomVM00000109_Start';
  procedure VM_FISH_RED_END; external SecureEngine name 'CustomVM00000109_End';
  procedure VM_FISH_BLACK_START; external SecureEngine name 'CustomVM00000111_Start';
  procedure VM_FISH_BLACK_END; external SecureEngine name 'CustomVM00000111_End';
  procedure VM_PUMA_WHITE_START; external SecureEngine name 'CustomVM00000113_Start';
  procedure VM_PUMA_WHITE_END; external SecureEngine name 'CustomVM00000113_End';
  procedure VM_PUMA_RED_START; external SecureEngine name 'CustomVM00000115_Start';
  procedure VM_PUMA_RED_END; external SecureEngine name 'CustomVM00000115_End';
  procedure VM_PUMA_BLACK_START; external SecureEngine name 'CustomVM00000117_Start';
  procedure VM_PUMA_BLACK_END; external SecureEngine name 'CustomVM00000117_End';
  procedure VM_SHARK_WHITE_START; external SecureEngine name 'CustomVM00000119_Start';
  procedure VM_SHARK_WHITE_END; external SecureEngine name 'CustomVM00000119_End';
  procedure VM_SHARK_RED_START; external SecureEngine name 'CustomVM00000121_Start';
  procedure VM_SHARK_RED_END; external SecureEngine name 'CustomVM00000121_End';
  procedure VM_SHARK_BLACK_START; external SecureEngine name 'CustomVM00000123_Start';
  procedure VM_SHARK_BLACK_END; external SecureEngine name 'CustomVM00000123_End';
  procedure VM_DOLPHIN_WHITE_START; external SecureEngine name 'CustomVM00000135_Start';
  procedure VM_DOLPHIN_WHITE_END; external SecureEngine name 'CustomVM00000135_End';
  procedure VM_DOLPHIN_RED_START; external SecureEngine name 'CustomVM00000137_Start';
  procedure VM_DOLPHIN_RED_END; external SecureEngine name 'CustomVM00000137_End';
  procedure VM_DOLPHIN_BLACK_START; external SecureEngine name 'CustomVM00000139_Start';
  procedure VM_DOLPHIN_BLACK_END; external SecureEngine name 'CustomVM00000139_End';
  procedure VM_EAGLE_WHITE_START; external SecureEngine name 'CustomVM00000147_Start';
  procedure VM_EAGLE_WHITE_END; external SecureEngine name 'CustomVM00000147_End';
  procedure VM_EAGLE_RED_START; external SecureEngine name 'CustomVM00000149_Start';
  procedure VM_EAGLE_RED_END; external SecureEngine name 'CustomVM00000149_End';
  procedure VM_EAGLE_BLACK_START; external SecureEngine name 'CustomVM00000151_Start';
  procedure VM_EAGLE_BLACK_END; external SecureEngine name 'CustomVM00000151_End';
  procedure VM_MUTATE_ONLY_START; external SecureEngine name 'Mutate_Start';
  procedure VM_MUTATE_ONLY_END; external SecureEngine name 'Mutate_End';
{$ELSE}
  procedure VM_TIGER_WHITE_START; external SecureEngine name 'CustomVM00000100_Start';
  procedure VM_TIGER_WHITE_END; external SecureEngine name 'CustomVM00000100_End';
  procedure VM_TIGER_RED_START; external SecureEngine name 'CustomVM00000101_Start';
  procedure VM_TIGER_RED_END; external SecureEngine name 'CustomVM00000101_End';
  procedure VM_TIGER_BLACK_START; external SecureEngine name 'CustomVM00000102_Start';
  procedure VM_TIGER_BLACK_END; external SecureEngine name 'CustomVM00000102_End';
  procedure VM_FISH_WHITE_START; external SecureEngine name 'CustomVM00000106_Start';
  procedure VM_FISH_WHITE_END; external SecureEngine name 'CustomVM00000106_End';
  procedure VM_FISH_RED_START; external SecureEngine name 'CustomVM00000108_Start';
  procedure VM_FISH_RED_END; external SecureEngine name 'CustomVM00000108_End';
  procedure VM_FISH_BLACK_START; external SecureEngine name 'CustomVM00000110_Start';
  procedure VM_FISH_BLACK_END; external SecureEngine name 'CustomVM00000110_End';
  procedure VM_PUMA_WHITE_START; external SecureEngine name 'CustomVM00000112_Start';
  procedure VM_PUMA_WHITE_END; external SecureEngine name 'CustomVM00000112_End';
  procedure VM_PUMA_RED_START; external SecureEngine name 'CustomVM00000114_Start';
  procedure VM_PUMA_RED_END; external SecureEngine name 'CustomVM00000114_End';
  procedure VM_PUMA_BLACK_START; external SecureEngine name 'CustomVM00000116_Start';
  procedure VM_PUMA_BLACK_END; external SecureEngine name 'CustomVM00000116_End';
  procedure VM_SHARK_WHITE_START; external SecureEngine name 'CustomVM00000118_Start';
  procedure VM_SHARK_WHITE_END; external SecureEngine name 'CustomVM00000118_End';
  procedure VM_SHARK_RED_START; external SecureEngine name 'CustomVM00000120_Start';
  procedure VM_SHARK_RED_END; external SecureEngine name 'CustomVM00000120_End';
  procedure VM_SHARK_BLACK_START; external SecureEngine name 'CustomVM00000122_Start';
  procedure VM_SHARK_BLACK_END; external SecureEngine name 'CustomVM00000122_End';
  procedure VM_DOLPHIN_WHITE_START; external SecureEngine name 'CustomVM00000134_Start';
  procedure VM_DOLPHIN_WHITE_END; external SecureEngine name 'CustomVM00000134_End';
  procedure VM_DOLPHIN_RED_START; external SecureEngine name 'CustomVM00000136_Start';
  procedure VM_DOLPHIN_RED_END; external SecureEngine name 'CustomVM00000136_End';
  procedure VM_DOLPHIN_BLACK_START; external SecureEngine name 'CustomVM00000138_Start';
  procedure VM_DOLPHIN_BLACK_END; external SecureEngine name 'CustomVM00000138_End';
  procedure VM_EAGLE_WHITE_START; external SecureEngine name 'CustomVM00000146_Start';
  procedure VM_EAGLE_WHITE_END; external SecureEngine name 'CustomVM00000146_End';
  procedure VM_EAGLE_RED_START; external SecureEngine name 'CustomVM00000148_Start';
  procedure VM_EAGLE_RED_END; external SecureEngine name 'CustomVM00000148_End';
  procedure VM_EAGLE_BLACK_START; external SecureEngine name 'CustomVM00000150_Start';
  procedure VM_EAGLE_BLACK_END; external SecureEngine name 'CustomVM00000150_End';
  procedure VM_MUTATE_ONLY_START; external SecureEngine name 'Mutate_Start';
  procedure VM_MUTATE_ONLY_END; external SecureEngine name 'Mutate_End';
{$ENDIF}
