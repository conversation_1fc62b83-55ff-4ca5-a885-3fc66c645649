# 运维流程处理工具

这是一个基于Python Tkinter的GUI工具，用于自动化处理运营和技术文件夹的签名前处理流程。

## 功能特性

1. **数据文件夹管理**
   - 支持选择和保存默认数据文件夹路径
   - 自动保存配置到本地文件

2. **文件夹浏览**
   - 树形视图显示文件夹结构，自动展开一级目录
   - 双击展开/折叠文件夹
   - 单击选择文件夹，点击"选择文件夹"按钮确认选择
   - 支持同时选择运营和技术文件夹
   - 颜色标识：运营文件夹（蓝色）、技术文件夹（绿色）
   - 支持取消选择和清除所有选择

3. **自动处理**
   - 自动识别运营文件夹（包含"运营"关键字）
   - 自动识别技术文件夹（包含"技术"关键字）
   - 自动解压ZIP和RAR文件
   - 查找运营文件夹中的data目录
   - 查找技术文件夹中的game.exe文件

4. **日志输出**
   - 实时显示处理进度和结果
   - 带时间戳的详细日志
   - 支持清除日志功能

## 安装依赖

### 基础运行（仅支持ZIP文件）
程序可以直接运行，无需额外依赖：
```bash
python maintenance_gui.py
```

### 支持RAR文件解压
为了支持RAR文件，请选择以下方案之一：

**方案1：安装WinRAR（推荐）**
- 下载安装：https://www.win-rar.com/
- 程序会自动检测WinRAR安装路径

**方案2：安装7-Zip**
- 下载安装：https://www.7-zip.org/
- 程序会自动检测7-Zip安装路径

**方案3：使用Python库**
```bash
pip install rarfile
```

程序会自动尝试多种解压方法，如果一种失败会尝试下一种。

## 使用方法

1. 运行程序：
   ```bash
   python maintenance_gui.py
   ```

2. 选择数据文件夹（程序会记住这个路径）

3. 在文件夹树中浏览并找到目标文件夹
   - 文件夹树会自动展开一级目录
   - 双击可以展开/折叠文件夹

4. 选择包含"运营"和"技术"关键字的文件夹
   - 单击选中文件夹
   - 点击"选择文件夹"按钮确认选择
   - 运营文件夹会显示为蓝色背景
   - 技术文件夹会显示为绿色背景
   - 可以同时选择运营和技术文件夹
   - 再次点击"选择文件夹"可以取消选择

5. 点击"签名前处理"开始自动处理

## 处理流程

### 运营文件夹处理
1. 自动解压文件夹中的ZIP/RAR文件
2. 在解压后的文件中查找data文件夹
3. 使用SFile打包功能处理data文件夹
4. 生成update.saf和update.sah文件
5. 创建patch~文件夹并移动打包文件

### 技术文件夹处理
1. 自动解压文件夹中的ZIP/RAR文件
2. 在解压后的文件中查找game.exe
3. 使用Themida.exe自动加壳保护
4. 生成game_protected.exe并重命名为game.exe
5. 将加壳后的文件复制到patch~目录

## 配置文件

程序会在当前目录生成`maintenance_config.json`配置文件，用于保存：
- 数据文件夹路径
- 其他用户设置

## 扩展功能

当前版本实现了：
- 基础的文件夹处理和文件查找功能
- **SFile打包功能** - 自动打包data文件夹生成update.saf和update.sah
- **Themida.exe自动加壳** - 使用简化命令行方式自动保护game.exe
- 自动创建patch~文件夹结构

后续可以集成：
- MD5计算和launcher.shaiya文件更新
- 自动化签名流程集成
- 更多自动化处理步骤

## 注意事项

1. 确保选择的文件夹名称包含"运营"或"技术"关键字
2. 压缩文件应该包含正确的目录结构
3. 运营文件夹解压后应该能找到唯一的data文件夹
4. 技术文件夹解压后应该能找到game.exe文件
