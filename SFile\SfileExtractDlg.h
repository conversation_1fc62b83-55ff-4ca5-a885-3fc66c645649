#if !defined(AFX_SFILEEXTRACTDLG_H__85591E4C_7ABF_4434_A8CC_D64BC6906F12__INCLUDED_)
#define AFX_SFILEEXTRACTDLG_H__85591E4C_7ABF_4434_A8CC_D64BC6906F12__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SfileExtractDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// SfileExtractDlg dialog
#include "SATreeCtrl.h"
#include <list>

class SfileExtractDlg : public CDialog
{
// Construction
public:
	bool LoadSAFile( char *filename );
	void ReadSAFile( int &fh, SADir *saDir );
	void ReadEmptyList( int &fh );
	void SetDlgItemEnable( BOOL flg );
	void ExtractFile( int &fh, char *path, SADir *dir, int index );
	SfileExtractDlg(CWnd* pParent = NULL);   // standard constructor
	void ShowFolder();
	void ExtractDir( int &fh, CString path, SADir *dir );
	//bool ChkFileExt( char *filename );
	void GetFileCnt( SADir *dir, DWORD &cnt );
	void OnButtonExtractAll();
	void DeleteFileList(SADir *dir);
	void Release();
	void SetTime( CString &fileName, SAFile *file );

	SADir *m_pSADir;
	unsigned int m_nCurCnt;
	CString m_strOpenFileName;

	std::list<CString> m_viewL;
	CString m_viewFolder;

	unsigned int m_nEmptyListSu;
	SAFile *m_pEmptyList;

	int		m_saType;

// Dialog Data
	//{{AFX_DATA(SfileExtractDlg)
	enum { IDD = IDD_DLG_SFILE_EXTRACT };
	CProgressCtrl	m_progress;
	CListBox	m_list;
	SATreeCtrl	m_tree;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SfileExtractDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(SfileExtractDlg)
	afx_msg void OnButtonFileOpen();
	afx_msg void OnButtonExtractDir();
	virtual BOOL OnInitDialog();
	afx_msg void OnButtonExtract();
	afx_msg void OnClose();
	afx_msg void OnDblclkListFile();
	afx_msg void OnDestroy();
	afx_msg void OnButtonEmptylist();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEEXTRACTDLG_H__85591E4C_7ABF_4434_A8CC_D64BC6906F12__INCLUDED_)
