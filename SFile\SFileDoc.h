// SFileDoc.h : interface of the CSFileDoc class
//
/////////////////////////////////////////////////////////////////////////////

#if !defined(AFX_SFILEDOC_H__C7B96048_49E4_4164_9FCE_032F5102DAE3__INCLUDED_)
#define AFX_SFILEDOC_H__C7B96048_49E4_4164_9FCE_032F5102DAE3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


class CSFileDoc : public CDocument
{
protected: // create from serialization only
	CSFileDoc();
	DECLARE_DYNCREATE(CSFileDoc)

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSFileDoc)
	public:
	virtual BOOL OnNewDocument();
	virtual void Serialize(CArchive& ar);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CSFileDoc();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:

// Generated message map functions
protected:
	//{{AFX_MSG(CSFileDoc)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEDOC_H__C7B96048_49E4_4164_9FCE_032F5102DAE3__INCLUDED_)
