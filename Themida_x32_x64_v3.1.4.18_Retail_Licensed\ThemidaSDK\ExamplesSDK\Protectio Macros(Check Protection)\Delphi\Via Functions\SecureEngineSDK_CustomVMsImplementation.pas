 // --- File generated automatically from Oreans VM Generator (8/7/2013) ---

{$IFDEF WIN64}
  procedure VM_TIGER_WHITE_START; external SecureEngine name 'CustomVM00000103_Start';
  procedure VM_TIGER_WHITE_END; external SecureEngine name 'CustomVM00000103_End';
  procedure VM_TIGER_RED_START; external SecureEngine name 'CustomVM00000104_Start';
  procedure VM_TIGER_RED_END; external SecureEngine name 'CustomVM00000104_End';
  procedure VM_TIGER_BLACK_START; external SecureEngine name 'CustomVM00000105_Start';
  procedure VM_TIGER_BLACK_END; external SecureEngine name 'CustomVM00000105_End';
{$ELSE}
  procedure VM_TIGER_WHITE_START; external SecureEngine name 'CustomVM00000100_Start';
  procedure VM_TIGER_WHITE_END; external SecureEngine name 'CustomVM00000100_End';
  procedure VM_TIGER_RED_START; external SecureEngine name 'CustomVM00000101_Start';
  procedure VM_TIGER_RED_END; external SecureEngine name 'CustomVM00000101_End';
  procedure VM_TIGER_BLACK_START; external SecureEngine name 'CustomVM00000102_Start';
  procedure VM_TIGER_BLACK_END; external SecureEngine name 'CustomVM00000102_End';
{$ENDIF}
