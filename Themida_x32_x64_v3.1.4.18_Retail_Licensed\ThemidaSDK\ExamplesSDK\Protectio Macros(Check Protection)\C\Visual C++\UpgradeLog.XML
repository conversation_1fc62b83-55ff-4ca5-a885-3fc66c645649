<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type='text/xsl' href='_UpgradeReport_Files/UpgradeReport.xslt'?><UpgradeLog>
<Properties><Property Name="Solution" Value="vc_example">
</Property><Property Name="Solution File" Value="C:\Users\<USER>\Desktop\WinLicenseFiles2000\WinlicenseSDK\ExamplesSDK\Macros\C\Visual C++\vc_example.sln">
</Property><Property Name="User Options File" Value="C:\Users\<USER>\Desktop\WinLicenseFiles2000\WinlicenseSDK\ExamplesSDK\Macros\C\Visual C++\vc_example.suo">
</Property><Property Name="Date" Value="lunes, 15 de diciembre de 2008">
</Property><Property Name="Time" Value="7:54">
</Property></Properties><Event ErrorLevel="0" Project="" Source="vc_example.sln" Description="File successfully backed up as C:\Users\<USER>\Desktop\WinLicenseFiles2000\WinlicenseSDK\ExamplesSDK\Macros\C\Visual C++\vc_example.sln.old">
</Event><Event ErrorLevel="0" Project="" Source="vc_example.suo" Description="File successfully backed up as C:\Users\<USER>\Desktop\WinLicenseFiles2000\WinlicenseSDK\ExamplesSDK\Macros\C\Visual C++\vc_example.suo.old">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Visual C++ now supports a secure version of the C Runtime Library. Use of this library is turned on by default. You may see some warnings about deprecated functions when you build your project. It is advised that you correct these warnings, in order to make your code more secure.">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="The C/C++ compiler default settings have been modified to be more compliant with ISO Standard C++. Included in those changes are enforcing Standard C++ for loop scoping and supporting wchar_t as a native type. These changes may cause existing code to no longer compile without changes to the code or the compiler options with which it is built.">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="The single-threaded run-time library switches (/MLd, /ML) have been removed from the C++ compiler.  The project has been automatically converted to use the corresponding multi-threaded run-time library switches (/MTd, /MT).">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="The C/C++ compiler switch /YX is lo longer supported. /YX has been removed from your project settings.">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="Due to the requirement that Visual C++ projects produce an embedded (by default) Windows SxS manifest, manifest files in the project are automatically excluded from building with the Manifest Tool.  It is recommended that the dependency information contained in any manifest files be converted to &quot;#pragma comment(linker,&quot;&lt;insert dependency here&gt;&quot;)&quot; in a header file that is included from your source code.  If your project already embeds a manifest in the RT_MANIFEST resource section through a resource (.rc) file, the line will need to be commented out before the project will build correctly.">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="Due to a conformance change in the C++ compiler, code change may be required before your project will build without errors.  Previous versions of the C++ compiler allowed specification of member function pointers by member function name (e.g. MemberFunctionName).  The C++ standard requires a fully qualified name with the use of the address-of operator (e.g. &amp;ClassName::MemberFunctionName).  If your project contains forms or controls used in the Windows Forms Designer, you may have to change code in InitializeComponent because the designer generated code used the non-conformant syntax in delegate construction (used in event handlers).">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Project file successfully backed up as 'C:\Users\<USER>\Desktop\WinLicenseFiles2000\WinlicenseSDK\ExamplesSDK\Macros\C\Visual C++\vc_example.vcproj.7.10.old'.">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Project upgraded successfully.">
</Event><Event ErrorLevel="3" Project="vc_example" Source="vc_example.vcproj" Description="Converted">
</Event><Event ErrorLevel="0" Project="" Source="vc_example.sln" Description="Solution converted successfully">
</Event><Event ErrorLevel="3" Project="" Source="vc_example.sln" Description="Converted">
</Event></UpgradeLog>