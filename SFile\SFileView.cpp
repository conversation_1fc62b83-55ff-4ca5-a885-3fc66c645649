// SFileView.cpp : implementation of the CSFileView class
//

#include "stdafx.h"
#include "SFile.h"

#include "SFileDoc.h"
#include "SFileView.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSFileView

IMPLEMENT_DYNCREATE(CSFileView, CView)

BEGIN_MESSAGE_MAP(CSFileView, CView)
	//{{AFX_MSG_MAP(CSFileView)
		// NOTE - the ClassWizard will add and remove mapping macros here.
		//    DO NOT EDIT what you see in these blocks of generated code!
	//}}AFX_MSG_MAP
	// Standard printing commands
	ON_COMMAND(ID_FILE_PRINT, CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_DIRECT, CView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_PREVIEW, CView::OnFilePrintPreview)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSFileView construction/destruction

CSFileView::CSFileView()
{
	// TODO: add construction code here

}

CSFileView::~CSFileView()
{
}

BOOL CSFileView::PreCreateWindow(CREATESTRUCT& cs)
{
	// TODO: Modify the Window class or styles here by modifying
	//  the CREATESTRUCT cs

	return CView::PreCreateWindow(cs);
}

/////////////////////////////////////////////////////////////////////////////
// CSFileView drawing

void CSFileView::OnDraw(CDC* pDC)
{
	CSFileDoc* pDoc = GetDocument();
	ASSERT_VALID(pDoc);
	// TODO: add draw code for native data here
}

/////////////////////////////////////////////////////////////////////////////
// CSFileView printing

BOOL CSFileView::OnPreparePrinting(CPrintInfo* pInfo)
{
	// default preparation
	return DoPreparePrinting(pInfo);
}

void CSFileView::OnBeginPrinting(CDC* /*pDC*/, CPrintInfo* /*pInfo*/)
{
	// TODO: add extra initialization before printing
}

void CSFileView::OnEndPrinting(CDC* /*pDC*/, CPrintInfo* /*pInfo*/)
{
	// TODO: add cleanup after printing
}

/////////////////////////////////////////////////////////////////////////////
// CSFileView diagnostics

#ifdef _DEBUG
void CSFileView::AssertValid() const
{
	CView::AssertValid();
}

void CSFileView::Dump(CDumpContext& dc) const
{
	CView::Dump(dc);
}

CSFileDoc* CSFileView::GetDocument() // non-debug version is inline
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CSFileDoc)));
	return (CSFileDoc*)m_pDocument;
}
#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CSFileView message handlers
