;; ------------------------------------------------------------
;
;   PureBasic - SecureEngine Mac<PERSON> for x64
;
;    (c) 2012 - Oreans Technologies
;
; ------------------------------------------------------------


Procedure VMStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 1
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure VMEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 2
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure CodeReplaceStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 3
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure CodeReplaceEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 4
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure RegisteredStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 5
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure RegisteredEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 6
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure EncodeStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 7
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure EncodeEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 8
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure ClearStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 9
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure ClearEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 10
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure UnregisteredStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 11
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure UnregisteredEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 12
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure RegisteredVMStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 13
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure RegisteredVMEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 14
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure UnprotectedStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 15
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure UnprotectedEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 16
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure MutateStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 21
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure MutateEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 22
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure StrEncryptStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 23
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure StrEncryptEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 24
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure StrEncryptWStart()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 27
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure StrEncryptWEnd()
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 28
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58

EndProcedure


Procedure SECheckProtection(*MyVar, MyCheckValue.l)
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 17
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58
  !mov [rcx], edx

EndProcedure


Procedure SECheckCodeIntegrity(*MyVar, MyCheckValue.l)
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 18
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58
  !mov [rcx], edx

EndProcedure


Procedure SECheckRegistration(*MyVar, MyCheckValue.l)
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 19
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58
  !mov [rcx], edx

EndProcedure


Procedure SECheckVirtualPC(*MyVar, MyCheckValue.l)
 
  !DB 0x50, 0x53, 0x51
  !DB 0xB8, 0x4C, 0x57, 0x4D, 0x54, 0xBB
  !DD 20
  !DB 0xB9, 0x4C, 0x57, 0x4D, 0x54
  !DB 0x03, 0xD8
  !DB 0x03, 0xC8
  !DB 0x59, 0x5B, 0x58
  !mov [rcx], edx

EndProcedure
; IDE Options = PureBasic 4.61 (Windows - x64)
; CursorPosition = 341
; FirstLine = 305
; Folding = -----
; EnableXP
; Executable = Example.exe