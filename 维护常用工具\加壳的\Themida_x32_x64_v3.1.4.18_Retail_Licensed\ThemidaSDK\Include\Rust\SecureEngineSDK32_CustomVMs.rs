// ******************************************************************************
// Header: SecureEngineSDK32_CustomVMs.rs
// Description: Rust macros definitions
//
// Author/s: Oreans Technologies 
// (c) 2021 Oreans Technologies
//
// --- File generated automatically from Oreans VM Generator (14/5/2021) ---
// ******************************************************************************

#[allow(dead_code)]
#[link(name="SecureEngineSDK32", kind="dylib")]
extern {
    #[link_name = "CustomVM00000100_Start"]
    fn VM_TIGER_WHITE_START();

    #[link_name = "CustomVM00000100_End"]
    fn VM_TIGER_WHITE_END();

    #[link_name = "CustomVM00000101_Start"]
    fn VM_TIGER_RED_START();

    #[link_name = "CustomVM00000101_End"]
    fn VM_TIGER_RED_END();

    #[link_name = "CustomVM00000102_Start"]
    fn VM_TIGER_BLACK_START();

    #[link_name = "CustomVM00000102_End"]
    fn VM_TIGER_BLACK_END();

    #[link_name = "CustomVM00000106_Start"]
    fn VM_FISH_WHITE_START();

    #[link_name = "CustomVM00000106_End"]
    fn VM_FISH_WHITE_END();

    #[link_name = "CustomVM00000108_Start"]
    fn VM_FISH_RED_START();

    #[link_name = "CustomVM00000108_End"]
    fn VM_FISH_RED_END();

    #[link_name = "CustomVM00000110_Start"]
    fn VM_FISH_BLACK_START();

    #[link_name = "CustomVM00000110_End"]
    fn VM_FISH_BLACK_END();

    #[link_name = "CustomVM00000112_Start"]
    fn VM_PUMA_WHITE_START();

    #[link_name = "CustomVM00000112_End"]
    fn VM_PUMA_WHITE_END();

    #[link_name = "CustomVM00000114_Start"]
    fn VM_PUMA_RED_START();

    #[link_name = "CustomVM00000114_End"]
    fn VM_PUMA_RED_END();

    #[link_name = "CustomVM00000116_Start"]
    fn VM_PUMA_BLACK_START();

    #[link_name = "CustomVM00000116_End"]
    fn VM_PUMA_BLACK_END();

    #[link_name = "CustomVM00000118_Start"]
    fn VM_SHARK_WHITE_START();

    #[link_name = "CustomVM00000118_End"]
    fn VM_SHARK_WHITE_END();

    #[link_name = "CustomVM00000120_Start"]
    fn VM_SHARK_RED_START();

    #[link_name = "CustomVM00000120_End"]
    fn VM_SHARK_RED_END();

    #[link_name = "CustomVM00000122_Start"]
    fn VM_SHARK_BLACK_START();

    #[link_name = "CustomVM00000122_End"]
    fn VM_SHARK_BLACK_END();

    #[link_name = "CustomVM00000134_Start"]
    fn VM_DOLPHIN_WHITE_START();

    #[link_name = "CustomVM00000134_End"]
    fn VM_DOLPHIN_WHITE_END();

    #[link_name = "CustomVM00000136_Start"]
    fn VM_DOLPHIN_RED_START();

    #[link_name = "CustomVM00000136_End"]
    fn VM_DOLPHIN_RED_END();

    #[link_name = "CustomVM00000138_Start"]
    fn VM_DOLPHIN_BLACK_START();

    #[link_name = "CustomVM00000138_End"]
    fn VM_DOLPHIN_BLACK_END();

    #[link_name = "CustomVM00000146_Start"]
    fn VM_EAGLE_WHITE_START();

    #[link_name = "CustomVM00000146_End"]
    fn VM_EAGLE_WHITE_END();

    #[link_name = "CustomVM00000148_Start"]
    fn VM_EAGLE_RED_START();

    #[link_name = "CustomVM00000148_End"]
    fn VM_EAGLE_RED_END();

    #[link_name = "CustomVM00000150_Start"]
    fn VM_EAGLE_BLACK_START();

    #[link_name = "CustomVM00000150_End"]
    fn VM_EAGLE_BLACK_END();

    #[link_name = "CustomVM00000160_Start"]
    fn VM_LION_WHITE_START();

    #[link_name = "CustomVM00000160_End"]
    fn VM_LION_WHITE_END();

    #[link_name = "CustomVM00000162_Start"]
    fn VM_LION_RED_START();

    #[link_name = "CustomVM00000162_End"]
    fn VM_LION_RED_END();

    #[link_name = "CustomVM00000164_Start"]
    fn VM_LION_BLACK_START();

    #[link_name = "CustomVM00000164_End"]
    fn VM_LION_BLACK_END();

    #[link_name = "Mutate_Start"]
    fn VM_MUTATE_ONLY_START();

    #[link_name = "Mutate_End"]
    fn VM_MUTATE_ONLY_END();

}
