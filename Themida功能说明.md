# Themida自动加壳功能说明

## 功能概述

本工具集成了Themida64.exe的命令行功能，用于自动对技术文件夹中的game.exe进行加壳保护，提高游戏的安全性。

## 工作流程

### 1. 自动检测和处理
- 程序会自动在技术文件夹中查找game.exe文件
- 找到game.exe后，自动启动Themida加壳流程

### 2. Themida加壳过程
1. **查找Themida64.exe**: 在指定路径查找Themida64.exe工具
2. **创建项目文件**: 生成临时的WinLicense项目配置文件(.wlx)
3. **执行加壳**: 使用命令行调用Themida64.exe进行加壳
4. **备用方案**: 如果项目文件方式失败，尝试简化的命令行方式
5. **文件处理**: 备份原文件，重命名加壳文件
6. **复制到patch~**: 将加壳后的文件复制到patch~目录

### 3. 输出文件
- `game_original.exe`: 原始game.exe的备份
- `game.exe`: 加壳后的文件（重命名后）
- `patch~/game.exe`: 复制到patch~目录的加壳文件

## Themida配置

### 默认保护设置
```ini
Protection=1              # 启用保护
AntiDebug=1               # 反调试
AntiDump=1                # 反转储
CheckVirtualPC=1          # 检测虚拟机
CheckDebugger=1           # 检测调试器
CheckKernelDebugger=1     # 检测内核调试器
EncryptImportTable=1      # 加密导入表
EncryptStringTable=1      # 加密字符串表
CompressCode=1            # 压缩代码
```

### 命令行参数
```bash
Themida64.exe /protect ProjectFile /inputfile InputFile /outputfile OutputFile /q
```

- `/protect`: 指定项目文件
- `/inputfile`: 输入文件路径
- `/outputfile`: 输出文件路径
- `/q`: 静默模式（无界面输出）

## 使用方法

1. **选择技术文件夹**: 确保文件夹名包含"技术"关键字
2. **自动解压**: 程序会自动解压文件夹中的压缩文件
3. **查找game.exe**: 程序会在解压后的文件中查找game.exe
4. **自动加壳**: 找到game.exe后自动开始Themida加壳
5. **查看结果**: 在日志中查看加壳进度和结果

## 文件路径配置

### Themida64.exe位置
默认路径：`f:\维护\维护常用工具\加壳的\Themida_x32_x64_v3.1.4.18_Retail_Licensed\Themida64.exe`

备选路径：
- 当前目录下的`Themida64.exe`
- `f:\维护\加壳的\Themida_x32_x64_v3.1.4.18_Retail_Licensed\Themida64.exe`

## 输出目录结构

```
技术文件夹/
├── 解压的文件/
│   ├── Client/
│   │   ├── game_original.exe  # 原始文件备份
│   │   └── game.exe           # 加壳后的文件
│   └── ...

脚本执行目录/
└── patch~/
    └── game.exe               # 复制的加壳文件
```

## 错误处理

### 常见错误及解决方案

1. **未找到Themida64.exe**
   - 检查Themida是否正确安装
   - 确认路径是否正确
   - 检查文件权限

2. **错误码9 - Invalid Project file**
   - 项目文件格式问题
   - 程序会自动尝试简化的命令行方式
   - 检查Themida许可证是否有效

3. **错误码10 - 许可证问题**
   - 确认Themida许可证是否有效且未过期
   - 检查许可证文件是否存在
   - 重新激活Themida

4. **加壳失败**
   - 确保game.exe没有被其他程序占用
   - 检查磁盘空间是否充足
   - 确认文件路径中没有特殊字符

5. **权限错误**
   - 以管理员身份运行程序
   - 确保对文件夹有读写权限

6. **文件被占用**
   - 关闭可能使用game.exe的程序
   - 重启后重试

## 安全特性

### Themida提供的保护
1. **代码混淆**: 使代码难以逆向分析
2. **反调试**: 检测和阻止调试器
3. **反转储**: 防止内存转储
4. **虚拟机检测**: 检测分析环境
5. **完整性检查**: 防止文件被修改

### 加壳效果
- 增加文件大小（通常增加20-50%）
- 轻微影响启动时间
- 显著提高逆向分析难度
- 保护知识产权和商业机密

## 性能考虑

### 优化建议
1. **选择性保护**: 只对关键模块进行加壳
2. **测试兼容性**: 确保加壳后程序正常运行
3. **性能测试**: 验证加壳对性能的影响
4. **定期更新**: 使用最新版本的Themida

## 注意事项

1. **备份重要**: 程序会自动备份原始文件为game_original.exe
2. **签名流程**: 加壳后需要发送给SP小组进行签名
3. **测试验证**: 建议在测试环境验证加壳效果
4. **许可证**: 确保Themida许可证有效且未过期
5. **兼容性**: 某些杀毒软件可能误报加壳文件

## 故障排除

### 日志分析
程序会详细记录每个步骤：
- Themida64.exe查找结果
- 项目文件创建状态
- 加壳命令执行过程
- 文件处理结果

### 手动验证
如果自动加壳失败，可以：
1. 检查生成的项目文件内容
2. 手动运行Themida64.exe命令
3. 验证输入文件是否有效
4. 检查输出目录权限

## 技术支持

如遇到问题，请检查：
1. 日志输出中的详细错误信息
2. Themida版本和许可证状态
3. 系统权限和文件访问权限
4. 防病毒软件的干扰情况
