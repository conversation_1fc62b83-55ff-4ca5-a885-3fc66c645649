// SFileView.h : interface of the CSFileView class
//
/////////////////////////////////////////////////////////////////////////////

#if !defined(AFX_SFILEVIEW_H__4104E8D7_40F2_48F4_AE97_1B18646BF2D4__INCLUDED_)
#define AFX_SFILEVIEW_H__4104E8D7_40F2_48F4_AE97_1B18646BF2D4__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


class CSFileView : public CView
{
protected: // create from serialization only
	CSFileView();
	DECLARE_DYNCREATE(CSFileView)

// Attributes
public:
	CSFileDoc* GetDocument();

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSFileView)
	public:
	virtual void OnDraw(CDC* pDC);  // overridden to draw this view
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	protected:
	virtual BOOL OnPreparePrinting(CPrintInfo* pInfo);
	virtual void OnBeginPrinting(CDC* pDC, CPrintInfo* pInfo);
	virtual void OnEndPrinting(CDC* pDC, CPrintInfo* pInfo);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CSFileView();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:

// Generated message map functions
protected:
	//{{AFX_MSG(CSFileView)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

#ifndef _DEBUG  // debug version in SFileView.cpp
inline CSFileDoc* CSFileView::GetDocument()
   { return (CSFileDoc*)m_pDocument; }
#endif

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEVIEW_H__4104E8D7_40F2_48F4_AE97_1B18646BF2D4__INCLUDED_)
