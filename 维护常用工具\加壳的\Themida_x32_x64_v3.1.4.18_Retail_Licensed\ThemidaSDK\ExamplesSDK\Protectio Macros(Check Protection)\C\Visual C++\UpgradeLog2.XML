<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type='text/xsl' href='_UpgradeReport_Files/UpgradeReport.xslt'?><UpgradeLog>
<Properties><Property Name="Solution" Value="vc_example">
</Property><Property Name="Archivo de la solución" Value="C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.sln">
</Property><Property Name="Archivo de opciones de usuario" Value="C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.suo">
</Property><Property Name="Date" Value="lunes, 08 de julio de 2013">
</Property><Property Name="Time" Value="18:15">
</Property><Property Name="Número de registro" Value="2">
</Property></Properties><Event ErrorLevel="2" Project="" Source="vc_example.sln" Description="Error al crear el archivo de copia de seguridad: C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.sln.old">
</Event><Event ErrorLevel="2" Project="" Source="vc_example.suo" Description="Error al crear el archivo de copia de seguridad: C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.suo.old">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Converting project file 'C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.vcproj'.">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="This application has been updated to include settings related to the User Account Control (UAC) feature of Windows Vista. By default, when run on Windows Vista with UAC enabled, this application is marked to run with the same privileges as the process that launched it. This marking also disables the application from running with virtualization. You can change UAC related settings from the Property Pages of the project.">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="VCWebServiceProxyGeneratorTool is no longer supported. The tool has been removed from your project settings.">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Web deployment to the local IIS server is no longer supported. The Web Deployment build tool has been removed from your project settings.">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="MSB8012: $(TargetPath) ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\.\Release\vc_example.exe') does not match the Linker's OutputFile property value '.\Release/vc_example.exe' ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\Release/vc_example.exe') in project configuration 'Release|Win32'. This may cause your project to build incorrectly. To correct this, please make sure that $(TargetPath) property value matches the value specified in %(Link.OutputFile).">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="MSB8012: $(TargetPath) ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\x64\Release\vc_example.exe') does not match the Linker's OutputFile property value '.\Release/vc_example.exe' ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\Release/vc_example.exe') in project configuration 'Release|x64'. This may cause your project to build incorrectly. To correct this, please make sure that $(TargetPath) property value matches the value specified in %(Link.OutputFile).">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="MSB8012: $(TargetPath) ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\.\Debug\vc_example.exe') does not match the Linker's OutputFile property value '.\Debug/vc_example.exe' ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\Debug/vc_example.exe') in project configuration 'Debug|Win32'. This may cause your project to build incorrectly. To correct this, please make sure that $(TargetPath) property value matches the value specified in %(Link.OutputFile).">
</Event><Event ErrorLevel="1" Project="vc_example" Source="vc_example.vcproj" Description="MSB8012: $(TargetPath) ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\x64\Debug\vc_example.exe') does not match the Linker's OutputFile property value '.\Debug/vc_example.exe' ('C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\Debug/vc_example.exe') in project configuration 'Debug|x64'. This may cause your project to build incorrectly. To correct this, please make sure that $(TargetPath) property value matches the value specified in %(Link.OutputFile).">
</Event><Event ErrorLevel="0" Project="vc_example" Source="vc_example.vcproj" Description="Done converting to new project file 'C:\Users\<USER>\Desktop\ThemidaFiles crossplatform\ThemidaSDK\ExamplesSDK\Macros(Check Protection)\C\Visual C++\vc_example.vcxproj'.">
</Event><Event ErrorLevel="3" Project="vc_example" Source="vc_example.vcproj" Description="Converted">
</Event><Event ErrorLevel="0" Project="" Source="vc_example.sln" Description="La solución se convirtió correctamente">
</Event><Event ErrorLevel="3" Project="" Source="vc_example.sln" Description="Converted">
</Event></UpgradeLog>