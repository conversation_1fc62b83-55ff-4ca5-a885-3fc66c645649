// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#define APSTUDIO_HIDDEN_SYMBOLS
#include "windows.h"
#undef APSTUDIO_HIDDEN_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDC_MYTESTRESOURCES MENU 
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "E&xit",                       IDM_EXIT
    END
    POPUP "&Help"
    BEGIN
        MENUITEM "&About ...",                  IDM_ABOUT
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Accelerator
//

IDC_MYTESTRESOURCES ACCELERATORS 
BEGIN
    "?",            IDM_ABOUT,              ASCII,  ALT
    "/",            IDM_ABOUT,              ASCII,  ALT
END


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOGEX 22, 17, 205, 235
STYLE DS_SETFONT | DS_MODALFRAME | DS_3DLOOK | DS_FIXEDSYS | DS_CENTER | 
    WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_OVERLAPPEDWINDOW | WS_EX_TOOLWINDOW
CAPTION "Macros Example"
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    GROUPBOX        "Encode Macro",IDC_STATIC,14,13,170,75
    PUSHBUTTON      "Execute Encode Macro #1",IDC_BUTTON_ENCODE1,47,27,100,
                    13
    PUSHBUTTON      "Execute Encode Macro #2",IDC_BUTTON_ENCODE2,47,45,100,
                    12
    PUSHBUTTON      "Execute Encode Macro #3",IDC_BUTTON_ENCODE3,47,63,100,
                    12
    GROUPBOX        "Clear Macro",IDC_STATIC,13,89,171,43
    PUSHBUTTON      "Execute Clear Macro",IDC_CLEAR,50,105,100,13
    GROUPBOX        "Remove Block Macro",IDC_STATIC,13,142,171,71
    PUSHBUTTON      "Execute CodeReplace #1",IDC_REMOVED1,47,156,100,13
    PUSHBUTTON      "Execute CodeReplace #2",IDC_REMOVED2,47,174,100,12
    PUSHBUTTON      "Execute CodeReplace #3",IDC_REMOVED3,47,192,100,12
END


#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#define APSTUDIO_HIDDEN_SYMBOLS\r\n"
    "#include ""windows.h""\r\n"
    "#undef APSTUDIO_HIDDEN_SYMBOLS\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        RIGHTMARGIN, 195
        BOTTOMMARGIN, 225
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE 
BEGIN
    IDS_APP_TITLE           "MytestResources"
    IDC_MYTESTRESOURCES     "MYTESTRESOURCES"
END

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

