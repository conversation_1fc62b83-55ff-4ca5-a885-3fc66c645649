<html>
<body>
<pre>
<h1>Build Log</h1>
<h3>
--------------------Configuration: vc_example - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "E:\DOCUME~1\RAFF1\CONFIG~1\Temp\RSP125.tmp" with contents
[
/nologo /MLd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_MBCS" /Fp"Debug/vc_example.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /GZ /c 
"C:\Myprojects\X-Protector\ExamplesSDK\LicenseGenDLL\VC\vc_example.cpp"
]
Creating command line "cl.exe @E:\DOCUME~1\RAFF1\CONFIG~1\Temp\RSP125.tmp" 
Creating temporary file "E:\DOCUME~1\RAFF1\CONFIG~1\Temp\RSP126.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:windows /incremental:yes /pdb:"Debug/vc_example.pdb" /debug /machine:I386 /out:"Debug/vc_example.exe" /pdbtype:sept 
".\Debug\StdAfx.obj"
".\Debug\vc_example.obj"
".\Debug\vc_example.res"
]
Creating command line "link.exe @E:\DOCUME~1\RAFF1\CONFIG~1\Temp\RSP126.tmp"
<h3>Output Window</h3>
Compiling...
vc_example.cpp
Linking...



<h3>Results</h3>
vc_example.exe - 0 error(s), 0 warning(s)
</pre>
</body>
</html>
