 // --- File generated automatically from Oreans VM Generator (16/6/2015) ---

procedure VM_TIGER_WHITE_START(); stdcall;
procedure VM_TIGER_WHITE_END(); stdcall;
procedure VM_TIGER_RED_START(); stdcall;
procedure VM_TIGER_RED_END(); stdcall;
procedure VM_TIGER_BLACK_START(); stdcall;
procedure VM_TIGER_BLACK_END(); stdcall;
procedure VM_FISH_WHITE_START(); stdcall;
procedure VM_FISH_WHITE_END(); stdcall;
procedure VM_FISH_RED_START(); stdcall;
procedure VM_FISH_RED_END(); stdcall;
procedure VM_FISH_BLACK_START(); stdcall;
procedure VM_FISH_BLACK_END(); stdcall;
procedure VM_PUMA_WHITE_START(); stdcall;
procedure VM_PUMA_WHITE_END(); stdcall;
procedure VM_PUMA_RED_START(); stdcall;
procedure VM_PUMA_RED_END(); stdcall;
procedure VM_PUMA_BLACK_START(); stdcall;
procedure VM_PUMA_BLACK_END(); stdcall;
procedure VM_SHARK_WHITE_START(); stdcall;
procedure VM_SHARK_WHITE_END(); stdcall;
procedure VM_SHARK_RED_START(); stdcall;
procedure VM_SHARK_RED_END(); stdcall;
procedure VM_SHARK_BLACK_START(); stdcall;
procedure VM_SHARK_BLACK_END(); stdcall;
procedure VM_DOLPHIN_WHITE_START(); stdcall;
procedure VM_DOLPHIN_WHITE_END(); stdcall;
procedure VM_DOLPHIN_RED_START(); stdcall;
procedure VM_DOLPHIN_RED_END(); stdcall;
procedure VM_DOLPHIN_BLACK_START(); stdcall;
procedure VM_DOLPHIN_BLACK_END(); stdcall;
procedure VM_EAGLE_WHITE_START(); stdcall;
procedure VM_EAGLE_WHITE_END(); stdcall;
procedure VM_EAGLE_RED_START(); stdcall;
procedure VM_EAGLE_RED_END(); stdcall;
procedure VM_EAGLE_BLACK_START(); stdcall;
procedure VM_EAGLE_BLACK_END(); stdcall;
procedure VM_MUTATE_ONLY_START(); stdcall;
procedure VM_MUTATE_ONLY_END(); stdcall;
