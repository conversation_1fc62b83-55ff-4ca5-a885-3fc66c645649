<?xml version='1.0' encoding='utf-8' ?>
<!-- C++Builder XML Project -->
<PROJECT>
  <MACROS>
    <VERSION value="BCB.06.00"/>
    <PROJECT value="Project1.exe"/>
    <OBJFILES value="Project1.obj Unit1.obj"/>
    <RESFILES value="Project1.res"/>
    <IDLFILES value=""/>
    <IDLGENFILES value=""/>
    <DEFFILE value=""/>
    <RESDEPEN value="$(RESFILES) Unit1.dfm"/>
    <LIBFILES value=""/>
    <LIBRARIES value=""/>
    <SPARELIBS value="vcl.lib rtl.lib"/>
    <PACKAGES value="vcl.bpi rtl.bpi dbrtl.bpi adortl.bpi vcldb.bpi vclx.bpi bdertl.bpi 
      vcldbx.bpi ibxpress.bpi dsnap.bpi cds.bpi bdecds.bpi qrpt.bpi teeui.bpi 
      teedb.bpi tee.bpi dss.bpi teeqr.bpi visualclx.bpi visualdbclx.bpi 
      dsnapcrba.bpi dsnapcon.bpi bcbsmp.bpi vclie.bpi xmlrtl.bpi inet.bpi 
      inetdbbde.bpi inetdbxpress.bpi inetdb.bpi nmfast.bpi webdsnap.bpi 
      bcbie.bpi websnap.bpi soaprtl.bpi dclocx.bpi dbexpress.bpi dbxcds.bpi 
      indy.bpi bcb2kaxserver.bpi"/>
    <PATHCPP value=".;"/>
    <PATHPAS value=".;"/>
    <PATHRC value=".;"/>
    <PATHASM value=".;"/>
    <DEBUGLIBPATH value="$(BCB)\lib\debug"/>
    <RELEASELIBPATH value="$(BCB)\lib\release"/>
    <LINKER value="ilink32"/>
    <USERDEFINES value="_DEBUG"/>
    <SYSDEFINES value="_RTLDLL;NO_STRICT;USEPACKAGES"/>
    <MAINSOURCE value="Project1.cpp"/>
    <INCLUDEPATH value="$(BCB)\Projects;$(BCB)\include;$(BCB)\include\vcl;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6;..\..\..\..\Include\C"/>
    <LIBPATH value="$(BCB)\Projects;$(BCB)\lib\obj;$(BCB)\lib;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6;..\..\..\..\Lib\OMF"/>
    <WARNINGS value="-w-par"/>
    <OTHERFILES value=""/>
  </MACROS>
  <OPTIONS>
    <IDLCFLAGS value="-I$(BCB)\Projects -I$(BCB)\include -I$(BCB)\include\vcl 
      -IC:\DCL7\madCollection\madBasic\BCB6 
      -IC:\DCL7\madCollection\madDisAsm\BCB6 
      -IC:\DCL7\madCollection\madExcept\BCB6 
      -IC:\DCL7\madCollection\madRemote\BCB6 
      -IC:\DCL7\madCollection\madKernel\BCB6 
      -IC:\DCL7\madCollection\madCodeHook\BCB6 
      -IC:\DCL7\madCollection\madSecurity\BCB6 
      -IC:\DCL7\madCollection\madShell\BCB6 -I..\..\..\..\Include\C -src_suffix 
      cpp -D_DEBUG -boa"/>
    <CFLAG1 value="-Od -H=$(BCB)\lib\vcl60.csm -Hc -Vx -Ve -X- -r- -a8 -b- -k -y -v -vi- -c 
      -tW -tWM"/>
    <PFLAGS value="-$YD -$W -$O- -$A8 -v -JPHNE -M"/>
    <RFLAGS value=""/>
    <AFLAGS value="/mx /w2 /zd"/>
    <LFLAGS value="-D&quot;&quot; -aa -Tpe -x -Gn -v"/>
    <OTHERFILES value=""/>
  </OPTIONS>
  <LINKER>
    <ALLOBJ value="c0w32.obj $(PACKAGES) Memmgr.Lib sysinit.obj $(OBJFILES)"/>
    <ALLRES value="$(RESFILES)"/>
    <ALLLIB value="$(LIBFILES) $(LIBRARIES) import32.lib cp32mti.lib"/>
    <OTHERFILES value=""/>
  </LINKER>
  <FILELIST>
      <FILE FILENAME="Project1.res" FORMNAME="" UNITNAME="Project1.res" CONTAINERID="ResTool" DESIGNCLASS="" LOCALCOMMAND=""/>
      <FILE FILENAME="Project1.cpp" FORMNAME="" UNITNAME="Project1" CONTAINERID="CCompiler" DESIGNCLASS="" LOCALCOMMAND=""/>
      <FILE FILENAME="Unit1.cpp" FORMNAME="Form1" UNITNAME="Unit1" CONTAINERID="CCompiler" DESIGNCLASS="" LOCALCOMMAND=""/>
  </FILELIST>
  <BUILDTOOLS>
  </BUILDTOOLS>

  <IDEOPTIONS>
[Version Info]
IncludeVerInfo=0
AutoIncBuild=0
MajorVer=1
MinorVer=0
Release=0
Build=0
Debug=0
PreRelease=0
Special=0
Private=0
DLL=0
Locale=3082
CodePage=1252

[Version Info Keys]
CompanyName=
FileDescription=
FileVersion=*******
InternalName=
LegalCopyright=
LegalTrademarks=
OriginalFilename=
ProductName=
ProductVersion=*******
Comments=

[Excluded Packages]
c:\program files (x86)\borland\cbuilder6\Bin\dclite60.bpl=Borland Integrated Translation Environment

[HistoryLists\hlIncludePath]
Count=2
Item0=$(BCB)\Projects;$(BCB)\include;$(BCB)\include\vcl;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6;..\..\..\..\Include\C\
Item1=$(BCB)\Projects;D:\WinLicense\WinlicenseSDK\ExamplesSDK\Macros\C\BCB;$(BCB)\include;$(BCB)\include\vcl;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6

[HistoryLists\hlLibraryPath]
Count=2
Item0=$(BCB)\Projects;$(BCB)\lib\obj;$(BCB)\lib;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6;..\..\..\..\Lib\OMF
Item1=$(BCB)\Projects;D:\WinLicense\WinlicenseSDK\ExamplesSDK\Macros\C\BCB;$(BCB)\lib\obj;$(BCB)\lib;C:\DCL7\madCollection\madBasic\BCB6;C:\DCL7\madCollection\madDisAsm\BCB6;C:\DCL7\madCollection\madExcept\BCB6;C:\DCL7\madCollection\madRemote\BCB6;C:\DCL7\madCollection\madKernel\BCB6;C:\DCL7\madCollection\madCodeHook\BCB6;C:\DCL7\madCollection\madSecurity\BCB6;C:\DCL7\madCollection\madShell\BCB6

[HistoryLists\hlDebugSourcePath]
Count=1
Item0=$(BCB)\source\vcl

[HistoryLists\hlConditionals]
Count=1
Item0=_DEBUG

[Debugging]
DebugSourceDirs=$(BCB)\source\vcl

[Parameters]
RunParams=
Launcher=
UseLauncher=0
DebugCWD=
HostApplication=
RemoteHost=
RemotePath=
RemoteLauncher=
RemoteCWD=
RemoteDebug=0

[Compiler]
ShowInfoMsgs=0
LinkDebugVcl=0
LinkCGLIB=0

[CORBA]
AddServerUnit=1
AddClientUnit=1
PrecompiledHeaders=1
  </IDEOPTIONS>
</PROJECT>