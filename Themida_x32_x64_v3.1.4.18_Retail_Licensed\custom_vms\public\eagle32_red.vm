/*********************************************************/
/* Machine: EAGLE32 (Red)
/*
/* File generated automatically from Oreans VM Generator
/* Please, do not edit
/*
/* (c) 2016 Oreans Technologies
/*********************************************************/

[Main Machine Info]

Name = (Red)
MachineId = 0xE37A2B05
MachineSignature = 0x6E3BB2BD
ProductSupport = WinLicense, Virtualizer, Themida
FileVersionEncoded = 0x76281934
HardwareEncryption = 97Jd7N1826GTuwemaps82ysklftK726Hg4g7a81ms623H529Ks728sntJKKIIslH6Ksy1nal4l25amdtB


[Main Machine Architecture]

Name = EAGLE32
Bits = 32
MaxCPUs = 1
Emulates = IA32
RevirtualizationLevel = 1


[Main Machine Stats]

MemoryUsage = 853 KB
Speed = 5
Complexity = 84
ScoreMultiplier = 1


[Host Machine]

MachineId = 0x3AFA5205
NumProcessors = 1


[Guest Machine 1]

MachineId = 0x92858B05


[Host Machine Processor]

InternalConversor = 0x31A3BC19
InternalConversorLevel = 1
InternalConversorInProlog = Yes
UsingMicroRegs = Yes
RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = Yes
EnableMicroInstructions = Yes
SmartInstructionsRelocation = Yes
EnableHandlerTimes = Yes
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = No
PermutateHandlers = No
MutateHandlers = No


[Guest1 Machine Processor]

RelocateRegs = Yes
RelocateStages = Yes
OpcodePermutation = Yes
RelocateHandlers = Yes
JoinUndefinedOpcodes = No
AllowAvidFields = Yes
ExpandedInstructionSet = Yes
MergeStages = Yes
EnableRevirtualization = Yes
EnableJoinHandlers = Yes
EnableStageGarbage = No
EnableMicroInstructions = No
SmartInstructionsRelocation = Yes
EnableHandlerTimes = Yes
EnableBreakPoints = No
EnableDebugMode = No
EnableInterruptTrace = No
EnableFakeJumps = No
EnableFakeConditionalJumps = No
PermutateHandlers = No
MutateHandlers = No


[Specific Host Opcodes Customization]

Group1Mnemonics = ADD, MOV, SUB, AND, XOR, OR, POP, PUSH
Group1Garbage = [3]
Group1Avid = [3..6]
Group1Times = [5..10]

Group2Mnemonics = ENCRYPTED_OP_0x0000, ENCRYPTED_OP_0x0001, ENCRYPTED_OP_0x0002, ENCRYPTED_OP_0x0003, ENCRYPTED_OP_0x0004, ENCRYPTED_OP_0x0005
Group2Garbage = [3]
Group2Avid = [3..6]
Group2Times = [5..10]

Group3Mnemonics = ROL, ROR, RCL, SHL, RCR, SHR, MOVZX, MOVSX
Group3Garbage = [2]
Group3Avid = [1..2]
Group3Times = [2..5]

Group4Mnemonics = CMP, TEST, DEC, INC, NOT, NEG
Group4Garbage = [2]
Group4Avid = [1..3]
Group4Times = [2..5]

Group5Mnemonics = IMUL, LODSB, LODSW, LODSD, SCASB, SCASW, SCASD, CMPSB, CMPSW, CMPSD, STOSB, STOSW, STOSD, MOVSB, MOVSW, MOVSD, PUSHFD, POPFD, SCASQ, CMPSQ, MOVSQ
Group5Garbage = [2]
Group5Avid = [1..3]
Group5Times = [2..5]

Group6Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group6Garbage = [3]
Group6Avid = [0..0]
Group6Times = [3..7]

Group7Mnemonics = ENCRYPTED_OP_0x0006, ENCRYPTED_OP_0x0008
Group7Garbage = [3]
Group7Avid = [0..0]
Group7Times = [3..7]

Group8Mnemonics = CLC, CLD, CLI, CMC, STC, STD, STI
Group8Garbage = [3]
Group8Avid = [0..0]
Group8Times = [3..7]

Group9Mnemonics = ENCRYPTED_OP_0x0007, ENCRYPTED_OP_0x0009
Group9Garbage = [3]
Group9Avid = [0..0]
Group9Times = [1..1]


[Specific Guest1 Opcodes Customization]

Group1Mnemonics = COMMON_BINARY_OP, COMMON_UNARY_OP, POP, PUSH
Group1Garbage = [2]
Group1Avid = [2..5]
Group1Times = [5..8]

Group2Mnemonics = LODSB, LODSW, LODSD, SCASB, SCASW, SCASD, CMPSB, CMPSW, CMPSD, STOSB, STOSW, STOSD, MOVSB, MOVSW, MOVSD, PUSHFD, POPFD
Group2Garbage = [2]
Group2Avid = [2..3]
Group2Times = [2..7]

Group3Mnemonics = JCC_INSIDE, JUMP_OUTSIDE, JUMP_INSIDE, CALL, UNDEF, RET, JCC_OUTSIDE
Group3Garbage = [2]
Group3Avid = [0..0]
Group3Times = [2..6]


