#if !defined(AFX_SFILEDELETELISTDLG_H__5D91AA5B_00C6_43E5_BE7C_656E2386108C__INCLUDED_)
#define AFX_SFILEDELETELISTDLG_H__5D91AA5B_00C6_43E5_BE7C_656E2386108C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SfileDeleteListDlg.h : header file
//

#include "DeleteList.h"


/////////////////////////////////////////////////////////////////////////////
// SfileDeleteListDlg dialog


class SfileDeleteListDlg : public CDialog
{
// Construction
public:
	SfileDeleteListDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(SfileDeleteListDlg)
	enum { IDD = IDD_DLG_SFILE_DELETELIST };
	CDeleteList	m_list;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(SfileDeleteListDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(SfileDeleteListDlg)
	virtual void OnOK();
	afx_msg void OnButtonRootDir();
	afx_msg void OnButtonDeletelistAdd();
	afx_msg void OnButtonDeletelistDelete();
	afx_msg void OnButtonDeletelistDir();
	afx_msg void OnButtonDeletelistSave();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SFILEDELETELISTDLG_H__5D91AA5B_00C6_43E5_BE7C_656E2386108C__INCLUDED_)
